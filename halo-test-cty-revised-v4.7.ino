#include <EEPROM.h>
#include <avr/sleep.h>
#include <avr/interrupt.h>
#include <avr/wdt.h>
#include <math.h>
#include <avr/pgmspace.h> 
#include <util/atomic.h>  
/*
 * DỰ ÁN ĐÈN NGỦ CẢM ỨNG SANG TRỌNG - 4 LED (3 BÔNG + 1 NỀN)
 * Version: 4.5 - Critical Fix: CHANGE Interrupt Wake-up Version: 4.4 - Wait for Touch Release After Wake-up Smooth Breathing
 * 
 * TÍNH NĂNG CHÍNH:
 * - Khởi động: Tất cả LED tắt khi cấp nguồn.
 * - Chạm 1 lần đầu tiên: Bật đèn với chế độ và độ sáng đã lưu cuối cùng.
 * - 4 chế độ: Slow Fade (multi-phase, LED1-3 only, 2-step sync), Breathing (LED1-3 only), Rain Drops, Pattern Wave (LED1-3 only)
 * - Chạm 1 lần (sau khi đã bật): <PERSON><PERSON><PERSON><PERSON> chế độ (với transition thông minh)
 * - Chạm 2 lần: <PERSON><PERSON><PERSON> chế độ ngủ sâu (đ<PERSON><PERSON> tắt)
 * - Chạm giữ: Điều chỉnh độ sáng (chỉ LED4)
 * - Chạm 5 lần: Soft reset về mặc định
 * - Chạm 7 lần: Factory reset (hard reset với watchdog)
 * - LED4 hoạt động độc lập: Chỉ hiển thị độ sáng khi Dimming, giữ nguyên độ sáng trong các chế độ khác (bao gồm Slow Fade) và KHÔNG fade khi chuyển mode.
 * - Hiệu ứng chuyển đổi mượt mà, thông minh.
 * - Tối ưu hóa và sửa lỗi nghiêm trọng dựa trên báo cáo kiểm tra chi tiết.
 * - Tích hợp hệ thống debug và test nâng cao (commented out for production).
 * - Tối ưu hóa EEPROM write verification (với blacklisting) và Energy Saving sleep.
 * - Giải phóng chân debug khi không ở chế độ DEBUG.
 * - Sửa lỗi logic cho cảm biến chạm TTP223 hoạt động ở chế độ Active HIGH.
 * - Đảm bảo LED không tắt hoàn toàn (luôn ở MIN_BRIGHTNESS khi ở trạng thái "tắt" trong các mode).
 * - Sửa lỗi treo khi thức dậy từ chế độ ngủ (áp dụng logic từ mã tham khảo).
 */

// === DEBUG & TEST FLAGS (Comment out for Production) ===
// #define DEBUG         // Kích hoạt Serial output và debug pins
// #define RUN_TESTS     // Chạy comprehensive test trong setup()
// #define DEBUG_MEMORY  // Kích hoạt memory monitoring
// #define DEBUG_TOUCH   // Kích hoạt touch sensor debug prints
// #define DEBUG_FADES   // Kích hoạt fade system debug prints
// #define DEBUG_STATES  // Kích hoạt state machine debug prints
// #define DEBUG_MODE1   // Kích hoạt Slow Fade state machine debug prints

// Flash memory optimization
#ifndef F_CPU
#define F_CPU 16000000UL
#endif
#pragma GCC optimize ("Os")
#pragma GCC diagnostic ignored "-Wvolatile"
#pragma GCC push_options
#pragma GCC optimize ("O2")

// Compile-time checks
#ifndef PI
#define PI 3.14159265358979323846f
#endif

//=========================================================
// === PHẦN 1: KHAI BÁO VÀ CẤU HÌNH ===
//=========================================================

// === ĐỊNH NGHĨA CÁC TRẠNG THÁI CHÍNH CỦA HỆ THỐNG ===
enum SystemState {
  STATE_SLEEP,
  STATE_WAKE_UP, // Momentary state during wake-up handling
  STATE_WAITING_FOR_RELEASE, // <<< ADDED v4.4: State after wake-up, waiting for touch release
  STATE_IDLE, 
  STATE_MODE_ACTIVE,
  STATE_DIMMING,
  STATE_TRANSITIONING 
};

SystemState currentSystemState = STATE_SLEEP; // Start in sleep conceptually
SystemState previousSystemState = STATE_SLEEP;

// === CẤU HÌNH CHÂN ===
const int touchPin = 2; // INT0
const int ledPins[4] = {9, 10, 11, 6}; // PWM pins (LED1=9, LED2=10, LED3=11, LED4=6)
const int UNUSED_ANALOG_PIN = A0; // For randomSeed

// === DEBUG PINS (Only active if DEBUG is defined) ===
#ifdef DEBUG
const int DEBUG_PIN_FADE_START = 7;
const int DEBUG_PIN_MODE_CHANGE = 8;
const int DEBUG_PIN_TOUCH_EVENT = 12;
#endif

// === HẰNG SỐ EEPROM ===
const unsigned long EEPROM_WRITE_THROTTLE_MS = 30000; 
const int EEPROM_CELL_COUNT = 8; 
const int EEPROM_BLOCK_SIZE = 3; 
const int EEPROM_START_ADDR = 0;
const uint8_t EEPROM_MAX_RETRIES = 5; 

// === HẰNG SỐ THỜI GIAN ===
const unsigned long FADE_INTERVAL = 10;         
const unsigned long DIM_INTERVAL = 40;           // ✅ FIX: 50ms → 40ms
const unsigned long HOLD_THRESHOLD = 1000;      
// const unsigned long RESET_THRESHOLD = 7000;  // No longer needed - Factory Reset now uses 7 taps
const unsigned long DEBOUNCE_DELAY = 50;
const unsigned long TAP_TIMEOUT = 500;          
const unsigned long SLEEP_FADE_DURATION = 1500; 
const unsigned long WAKE_UP_IDLE_DELAY = 100;   
const unsigned long TRANSITION_TIMEOUT = 5000; // Increased timeout for complex transitions
const unsigned long MODE1_HOLD_DURATION = 10000; // 10 seconds hold at 100%
const unsigned long MODE1_TRANSITION_DURATION = 1000; // Duration for sync/fade phases
const unsigned long POWER_ON_FADE_DURATION = 1000; // Duration for fade-in on first touch

// === PREMIUM TIMING CONSTANTS ===
const unsigned long ULTRA_SMOOTH_INTERVAL = 40;  // 25fps for premium feel
const unsigned long GAMMA_CORRECTION_STEPS = 30; // 30 steps = 1.2s total
const unsigned long PREMIUM_FADE_BASE_DURATION = 1200; // Base duration for smooth fades

// === HẰNG SỐ TIẾT KIỆM NĂNG LƯỢNG ===
const unsigned long ENERGY_SAVE_3MIN = 180000UL;
const unsigned long ENERGY_SAVE_5MIN = 300000UL;
const unsigned long ENERGY_SAVE_8MIN = 480000UL;
const unsigned long ENERGY_SAVE_1HOUR = 3600000UL;
const unsigned long ENERGY_SAVE_2HOUR = 7200000UL;
const unsigned long ENERGY_SAVE_3HOUR = 10800000UL;
const unsigned long ENERGY_SAVE_5HOUR = 18000000UL;

// === HẰNG SỐ CHẾ ĐỘ ===
// Mode 1 cycle duration is now managed by its state machine
const unsigned long MODE2_BREATH_CYCLE = 3000;   
const unsigned long BREATHING_UPDATE_INTERVAL = 40; // <<< ADDED v4.5: Update rate for smooth breathing (25fps)
const unsigned long MODE3_DROP_INTERVAL = 800;   
const unsigned long MODE4_PATTERN_DURATION = 1000; 
const unsigned long MODE_TRANSITION_FADE_OUT_DURATION = 500; 

// === HẰNG SỐ ĐỘ SÁNG ===
const int MIN_BRIGHTNESS = 10; 
const int MAX_BRIGHTNESS = 255;
const int DEFAULT_BRIGHTNESS = 200;
const int DIM_MIN_BRIGHTNESS = 26;  // 10% of 255 (10% = 25.5 ≈ 26)
const byte DEFAULT_MODE = 1;
const float NORMAL_PERCENT = 0.9f; // Target brightness for LED1-3 in Mode 1 final phase
const float LED4_IDLE_PERCENT = 0.9f; // Target brightness for LED4 when idle in modes 1, 2, 3, 4

// ============================================
// 🔧 CRITICAL FIX: Energy Saving Progressive Constants
// ============================================
// Progressive Timeline Constants for Energy Saving
const float DIM_3_TO_5MIN_LED4_PERCENT = 0.8f;    // 3-5min: LED4 only
const float DIM_5_TO_8MIN_LED4_PERCENT = 0.7f;    // 5-8min: LED4 only  
const float DIM_8MIN_LED13_PERCENT = 0.9f;        // 8min-1h: LED1-3 start
const float DIM_8MIN_LED4_PERCENT = 0.7f;         // 8min-1h: LED4 continue
const float DIM_1HOUR_LED13_PERCENT = 0.85f;      // 1-2h: LED1-3 continue
const float DIM_1HOUR_LED4_PERCENT = 0.7f;        // 1-2h: LED4 maintain
const float DIM_2HOUR_LED13_PERCENT = 0.8f;       // 2-3h: LED1-3 more dim
const float DIM_2HOUR_LED4_PERCENT = 0.6f;        // 2-3h: LED4 more dim
const float DIM_3HOUR_LED13_PERCENT = 0.75f;      // 3-5h: LED1-3 final
const float DIM_3HOUR_LED4_PERCENT = 0.4f;        // 3-5h: LED4 final

// === HẰNG SỐ MỨC ƯU TIÊN FADE ===
#define PRIO_DEFAULT 1
#define PRIO_PATTERN 2
#define PRIO_DIMMING 3
#define PRIO_TRANSITION 4
#define PRIO_CRITICAL 5 

// === PATTERN CHO CHẾ ĐỘ 4 (Stored in PROGMEM) ===
const PROGMEM uint8_t LED_PATTERNS[][3] = {
  {1, 0, 0}, {0, 1, 0}, {0, 0, 1}, {1, 1, 0}, {0, 1, 1}, {1, 0, 1}
};
const uint8_t NUM_PATTERNS = sizeof(LED_PATTERNS) / sizeof(LED_PATTERNS[0]);

// === GAMMA CORRECTION TABLE (Stored in PROGMEM) ===
const PROGMEM uint8_t gamma8[] = {
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1,  1,  1,
  1,  1,  1,  1,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,  2,
  2,  3,  3,  3,  3,  3,  3,  3,  4,  4,  4,  4,  4,  5,  5,  5,
  5,  6,  6,  6,  6,  7,  7,  7,  7,  8,  8,  8,  9,  9,  9, 10,
 10, 10, 11, 11, 11, 12, 12, 13, 13, 13, 14, 14, 15, 15, 16, 16,
 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 24, 24, 25,
 25, 26, 27, 27, 28, 29, 29, 30, 31, 32, 32, 33, 34, 35, 35, 36,
 37, 38, 39, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 50,
 51, 52, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68,
 69, 70, 72, 73, 74, 75, 77, 78, 79, 81, 82, 83, 85, 86, 87, 89,
 90, 92, 93, 95, 96, 98, 99,101,102,104,105,107,109,110,112,114,
115,117,119,120,122,124,126,127,129,131,133,135,137,138,140,142,
144,146,148,150,152,154,156,158,160,162,164,167,169,171,173,175,
177,180,182,184,186,189,191,193,196,198,200,203,205,208,210,213,
215,218,220,223,225,228,231,233,236,239,241,244,247,249,252,255
};

// === BIẾN TRẠNG THÁI ===
uint8_t currentMode = DEFAULT_MODE;
uint8_t ledBrightness = DEFAULT_BRIGHTNESS; // Overall brightness setting
int ledState[4] = {0, 0, 0, 0}; // Current actual value of each LED (before gamma)
int targetLedState[4] = {0, 0, 0, 0}; // Target value for modes (used by dimming)
bool systemPoweredOn = false; // Controls if LEDs are active

// === BIẾN LƯU TRẠNG THÁI TRƯỚC KHI SLEEP ===
static uint8_t savedMode = DEFAULT_MODE;
static uint8_t savedBrightness = DEFAULT_BRIGHTNESS;

// === BIẾN CHO CÁC CHẾ ĐỘ (Sử dụng static để tránh stack o// Mode 1: Slow Fade state
enum SlowFadePhase { 
  M1_IDLE, 
  M1_ANALYZE_CURRENT,     // NEW: Analyze current LED states
  M1_SYNC_TO_MAX,         // Sync dim LEDs to brightest
  M1_FADE_TO_TARGET,      // Fade to user brightness or 100%
  M1_HOLD_AT_TARGET,      // Hold for 10 seconds  
  M1_FADE_TO_FINAL,       // Fade to final settle brightness
  M1_SETTLED              // Final settled state
};
SlowFadePhase mode1_currentPhase = M1_IDLE;
unsigned long mode1_phaseStartTime = 0;

// Mode 2: Breathing state
float mode2_breathingProgress = 0.0f;
unsigned long mode2_lastUpdateTime = 0;
static bool mode2_needsTimingReset = true; // <<< ADDED v4.7: Flag to signal timing reset needed
static float breathingEnergySaveFactor = 1.0f; // Factor để điều chỉnh breathing brightness
static unsigned long mode2_breathingStartTime = 0;
static unsigned long mode2_lastBreathingUpdate = 0;

// Mode 3: Rain Drops state
byte mode3_dropState = 0; // 0: Idle, 1: Fading In, 2: Fading Out
unsigned long mode3_lastDropTime = 0;
int mode3_activeDropLed = -1;


// Mode 4 (Pattern Wave)
static uint8_t mode4_currentPatternIndex = 0;
static unsigned long mode4_lastPatternTime = 0;

// Transition
enum TransitionPhase {
    TRANS_FADE_OUT,
    TRANS_FADE_IN,
    TRANS_COMPLETE
};
static TransitionPhase transitionCurrentPhase = TRANS_FADE_OUT;
static unsigned long transitionStartTime = 0; 
static byte transitionFromMode = 0;
static byte transitionToMode = 0;

// === BIẾN ĐIỀU KHIỂN CHẠM ===
unsigned long lastTouchTime = 0; 
unsigned long touchStartTime = 0; 
unsigned long lastDebounceTime = 0; 
int lastSteadyState = LOW; 
int lastFlickerableState = LOW; 
unsigned long lastTapTime = 0; 
uint8_t tapCount = 0;
bool pendingStateChange = false; 

// === ENHANCED FADE STRUCTURE ===
enum FadeType {
  FADE_LINEAR = 0,
  FADE_GAMMA_SMOOTH = 1,
  FADE_BREATHING = 2,
  FADE_SMART_CONTEXT = 3
};

struct FadeStage {
  int target_val;
  unsigned long duration_ms;
  uint8_t easing_type; // 0=linear, 1=cubic, 2=sine
};

struct FadeState {
  uint8_t active : 1;
  uint8_t priority : 3;
  uint8_t fade_type : 2;
  uint8_t current_stage : 2;
  
  int from_val;
  int to_val;
  unsigned long start_time_ms;
  unsigned long duration_ms;
  
  // Multi-stage support
  uint8_t stage_count;
  FadeStage stages[3]; // Max 3 stages per fade
};
FadeState fades[4];

// === BIẾN ĐIỀU KHIỂN DIM ===
unsigned long lastDimUpdate = 0;
static int dimLed4TargetBrightness = 0; // Target brightness for LED4 during dimming

// ============================================
// 🔧 CRITICAL DIMMING FIX: Add State Machine Variables
// ============================================
enum DimmingState {
  DIM_IDLE,
  DIM_PRE_FADE,      // Pre-fade if LEDs are OFF
  DIM_READY,         // Ready for dimming  
  DIM_ACTIVE,        // Actively dimming
  DIM_FINISHING      // Finishing and saving
};

DimmingState dimState = DIM_IDLE;
unsigned long dimStateStartTime = 0;
int dimStartBrightness = 0;      // Starting brightness when dim began
int dimTargetBrightness = 0;     // Target to dim towards  
bool dimTowardsMin = false;      // Direction: true = toward MIN, false = toward MAX
unsigned long dimPreFadeStartTime = 0;
static const unsigned long DIM_PRE_FADE_DURATION = 1000; // 1 second pre-fade
// ✅ ADD: LED4 chosen brightness storage
static int led4ChosenBrightness = 0;    // Stores user's chosen LED4 brightness from DIM

// === BIẾN ĐIỀU KHIỂN NHÁY (Blink) ===
uint8_t blinkPinIndex = 0;
uint8_t blinkTimesTotal = 0;
uint8_t blinkCountCurrent = 0;
int blinkOriginalState = 0;
unsigned long blinkStartTime = 0;
unsigned long blinkInterval = 150;
bool blinkIsOn = false;

// ============================================
// 🔧 CRITICAL FIX: Grace Period System Variables
// ============================================
// === BIẾN TIẾT KIỆM NĂNG LƯỢNG ===
uint8_t energySaveLevel = 0;
unsigned long lastInteractionTime = 0;

// Grace Period System
bool gracePeriodActive = false;
unsigned long gracePeriodEndTime = 0;
static const unsigned long GRACE_PERIOD_DURATION = 300000UL; // 5 minutes

// === INTERRUPT SAFE FLAGS ===
volatile bool wakeUpFlag = false; 

// === NON-VOLATILE FLAGS (using a struct for organization) ===
struct StatusFlags {
  uint8_t systemSleep : 1;     
  uint8_t dimActive : 1;       
  uint8_t dimDirectionUp : 1;  
  uint8_t isBlinking : 1;      
  uint8_t eepromWritePending : 1; 
  uint8_t touchState : 1;      // 1 = HIGH (touched), 0 = LOW (not touched)
  // uint8_t justWokeUp : 1;      // <<< REMOVED v4.1: No longer needed
  uint8_t reserved : 2;        // Adjusted reserved bits
};
volatile StatusFlags flags = {0}; // Make the whole struct volatile for ISR access

// === LED4 CONTROLLER & BRIGHTNESS MANAGEMENT ===
struct LED4Controller {
  uint8_t backgroundLevel;        // Current background brightness
  uint8_t targetLevel;           // Target brightness  
  bool isIndependent;            // True = không tham gia mode effects
  bool needsUpdate;              // Flag for smooth updates
  unsigned long lastUpdate;      // Timing control
};

LED4Controller led4Ctrl = {
  .backgroundLevel = 0,
  .targetLevel = 0, 
  .isIndependent = true,
  .needsUpdate = false,
  .lastUpdate = 0
};

struct BrightnessProfile {
  uint8_t userSetBrightness;      // What user actually configured
  uint8_t mode1FinalTarget;       // Mode 1 final settled brightness
  uint8_t led4BackgroundLevel;    // LED4 background brightness
  bool hasValidEEPROM;            // EEPROM data validity
  unsigned long lastChangeTime;   // For smart EEPROM writing
  uint8_t lastSavedBrightness;    // To detect ±3% changes
};

BrightnessProfile brightProfile = {
  .userSetBrightness = DEFAULT_BRIGHTNESS,
  .mode1FinalTarget = DEFAULT_BRIGHTNESS,
  .led4BackgroundLevel = DEFAULT_BRIGHTNESS,
  .hasValidEEPROM = false,
  .lastChangeTime = 0,
  .lastSavedBrightness = DEFAULT_BRIGHTNESS
};

// === FEATURE FLAGS (Memory-aware) ===
bool enableMultiStageFades = true;
bool enableUltraSmoothing = true;
bool enableAdvancedTransitions = true;

// === BIẾN EEPROM ===
static uint8_t lastSavedMode = 0;
static int lastSavedBrightness = -1;
static unsigned long lastEepromWriteTime = 0;
static uint8_t eepromNextCellIndex = 0; 
static uint8_t eepromCellRetries[EEPROM_CELL_COUNT] = {0}; 

// === WATCHDOG ===
// Watchdog is used to reset if the system hangs

// === KHAI BÁO PROTOTYPE ===
// Core
void setup();
void loop();
void enterState(SystemState newState);
void updateSystemState(unsigned long currentTime);

// Touch Input
void handleTouchInput(unsigned long currentTime);
void detectMultiTaps(unsigned long currentTime, bool touchPressed, bool touchReleased);
void processTapAction(uint8_t taps);

// Modes & Transitions
void setupMode(byte mode, bool isTransition = false);
void handleMode(unsigned long currentTime);
void handleMode1_SlowFade(unsigned long currentTime);
void handleMode2_Breathing(unsigned long currentTime);
void handleMode3_RainDrops(unsigned long currentTime);
void handleMode4_PatternWave(unsigned long currentTime);
void nextMode();
void handleModeTransition(byte fromMode, byte toMode);
void startTransition(byte fromMode, byte toMode);
void updateTransition(unsigned long currentTime);
void startIntelligentFadeIn(byte toMode); // Intelligent fade-in for different modes

// Fades & Enhanced Fade Engine
void startFade(int ledIndex, int toVal, unsigned long durVal, uint8_t priority = PRIO_DEFAULT, uint8_t fadeType = FADE_LINEAR);
void startMultiStageFade(int ledIndex, FadeStage* stages, uint8_t stageCount, uint8_t priority = PRIO_DEFAULT);
void startLeds123FadeTo(int toVal, unsigned long durVal, uint8_t priority = PRIO_DEFAULT);
void updateFades(unsigned long currentTime);
void cancelFade(int ledIndex);
void cancelAllFades();
bool areFadesActive(bool includeLed4 = true);
bool areFadesActiveOnLeds123();
float calculateEasedProgress(float t, uint8_t fadeType, uint8_t stageIndex);

// LED4 Controller
void setLED4Background(uint8_t brightness, bool immediate = false);
void updateLED4Controller(unsigned long currentTime);
uint8_t calculateLED4Level(uint8_t userBrightness);

// Brightness Management
void onBrightnessChanged(uint8_t newBrightness);
void initializeFeatures();
void checkMemoryForFeatures();

// Dimming
void handleDimming(unsigned long currentTime, bool touchHeld);
void startDimming();
void stopDimming();
void applyDimmingToAllLEDs(int newBrightness);
void updateTargetStatesAfterDim(int newBrightness);  // ✅ ADD if missing
int calculateDimStep(int currentBrightness);         // ✅ KEEP

// Blinking
void startBlink(int ledIndex, int times, unsigned long interval = 150);
void updateBlink(unsigned long currentTime);
void stopBlink();

// Sleep & Wake
void enterDeepSleep();
void handleWakeUp(unsigned long currentTime);
void wakeUpISR(); 

// EEPROM
void saveStateToEEPROM(bool forceSave = false);
void readStateFromEEPROM(byte &modeToSet, byte &brightnessToSet);
byte calculateCRC8(byte mode, byte brightness);

// Energy Saving
void handleEnergySave(unsigned long currentTime);
void applySavingLevel(byte level);

// Utilities
int gammaCorrect(int brightness);
unsigned long timeDiff(unsigned long current, unsigned long previous); 
void resetToDefault();
void applyBrightnessToLeds(); // Used mainly for initial setup/wake/power-on
void resetBreathingTiming(unsigned long currentTime); // Reset breathing timing function
float easeInOutSine(float t); 
float easeInOutCubic(float t);

// Debug (Optional - Macros defined as empty if DEBUG not defined)
#ifdef DEBUG
int getFreeMemory();
void reportMemoryUsage();
void printSystemState(SystemState stateToPrint); 
void systemHealthCheck();
void monitorMemory(); 
void debugFadeSystem();
void debugTouchSensor(); 
void printFullSystemState();
void verifyEEPROMIntegrity();
#ifdef DEBUG_MODE1
void printMode1State(SlowFadePhase phase);
#define DEBUG_MODE1_STATE_CHANGE(old, new) do { \
    DEBUG_TIMESTAMP(); \
    Serial.print(F("Mode1 Phase: ")); \
    printMode1State(old); \
    Serial.print(F(" -> ")); \
    printMode1State(new); \
    Serial.println(); \
} while(0)
#else
#define printMode1State(phase)
#define DEBUG_MODE1_STATE_CHANGE(old, new)
#endif // DEBUG_MODE1

// Debug macro with timestamp
#define DEBUG_TIMESTAMP() do { \
  Serial.print(millis()); \
  Serial.print(F(": ")); \
} while(0)

#define DEBUG_PRINT(x) do { DEBUG_TIMESTAMP(); Serial.print(F(x)); } while(0)
#define DEBUG_PRINTLN(x) do { DEBUG_TIMESTAMP(); Serial.println(F(x)); } while(0)
#define DEBUG_PRINT_VAR(x, y) do { DEBUG_TIMESTAMP(); Serial.print(F(x)); Serial.print(y); } while(0)
#define DEBUG_PRINTLN_VAR(x, y) do { DEBUG_TIMESTAMP(); Serial.print(F(x)); Serial.println(y); } while(0)

#define DEBUG_STATE_CHANGE(old, new) do { \
  DEBUG_TIMESTAMP(); \
  Serial.print(F("State: ")); \
  printSystemState(old); \
  Serial.print(F(" -> ")); \
  printSystemState(new); \
  Serial.println(); \
} while(0)
#else
// Define empty macros when DEBUG is off
#define DEBUG_TIMESTAMP()
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#define DEBUG_PRINT_VAR(x, y)
#define DEBUG_PRINTLN_VAR(x, y)
#define DEBUG_STATE_CHANGE(old, new)
#define reportMemoryUsage()
#define systemHealthCheck()
#define monitorMemory()
#define debugFadeSystem()
#define debugTouchSensor()
#define printFullSystemState()
#define verifyEEPROMIntegrity()
#define getFreeMemory() (0) // Return 0 if not debugging
#define printSystemState(state) // Empty definition
#define printMode1State(phase)
#define DEBUG_MODE1_STATE_CHANGE(old, new)
#endif

// Test Functions (Optional - Macros defined as empty if RUN_TESTS not defined)
#ifdef RUN_TESTS
void testModeTransitions();
void testEEPROM();
void runComprehensiveTest();
#else
#define testModeTransitions() 
#define testEEPROM()
#define runComprehensiveTest()
#endif

//=========================================================
// === PHẦN 2: SETUP ===
//=========================================================

void setup() {
#ifdef DEBUG
  Serial.begin(115200);
  unsigned long setupStart = millis();
  while (!Serial && (millis() - setupStart < 2000)); 
  DEBUG_PRINTLN("\n--- Booting Halo Lamp v3.9 (DEBUG) ---");
  reportMemoryUsage();
#endif

  // Initialize LED pins - Ensure they are OFF initially
  for (int i = 0; i < 4; i++) {
    pinMode(ledPins[i], OUTPUT);
    analogWrite(ledPins[i], 0);
    ledState[i] = 0;
    targetLedState[i] = 0;
    fades[i].active = false; 
  }

  // Initialize Touch pin (Active HIGH)
  pinMode(touchPin, INPUT);

  // Initialize flags
  flags = {0}; // Clear all flags
  flags.touchState = digitalRead(touchPin); 
  lastSteadyState = flags.touchState;
  lastFlickerableState = flags.touchState;

  // Read last state from EEPROM (but don't apply it yet)
  readStateFromEEPROM(currentMode, ledBrightness);
  DEBUG_PRINT_VAR("Read from EEPROM: Mode=", currentMode);
  DEBUG_PRINTLN_VAR(", Brightness=", ledBrightness);
  lastSavedMode = currentMode;
  lastSavedBrightness = ledBrightness;
  savedMode = currentMode;
  savedBrightness = ledBrightness;

  // Initialize Random Seed 
  randomSeed(analogRead(UNUSED_ANALOG_PIN) + micros()); 

  // Setup Watchdog Timer 
  wdt_enable(WDTO_2S);

  // Setup Debug Pins (Only if DEBUG is defined)
#ifdef DEBUG
  pinMode(DEBUG_PIN_FADE_START, OUTPUT);
  pinMode(DEBUG_PIN_MODE_CHANGE, OUTPUT);
  pinMode(DEBUG_PIN_TOUCH_EVENT, OUTPUT);
#endif

  // ✅ INITIALIZE new systems:
  initializeFeatures();
  
  // Read EEPROM and setup brightness profile
  brightProfile.userSetBrightness = ledBrightness;
  brightProfile.hasValidEEPROM = (ledBrightness != DEFAULT_BRIGHTNESS);
  onBrightnessChanged(ledBrightness); // Initialize all brightness targets

  // Initial state setup
  lastInteractionTime = millis();
  enterState(STATE_IDLE); // Enter IDLE state, but LEDs remain off
  systemPoweredOn = false; // Start with system logically OFF

#ifdef RUN_TESTS
  DEBUG_PRINTLN("--- Running Tests --- ");
  runComprehensiveTest();
  DEBUG_PRINTLN("--- Tests Complete --- ");
#endif

  DEBUG_PRINTLN("Setup complete. System OFF. Waiting for first touch.");
}

//=========================================================
// === PHẦN 3: MAIN LOOP ===
//=========================================================

void loop() {
  // Reset the watchdog timer at the start of every loop
  wdt_reset();

  unsigned long currentTime = millis();

  // <<< MODIFIED v3.9: Check wakeUpFlag atomically >>>
  bool localWakeUpFlag = false;
  ATOMIC_BLOCK(ATOMIC_RESTORESTATE) {
      if (wakeUpFlag) {
          localWakeUpFlag = true;
          wakeUpFlag = false; // Clear the flag atomically
      }
  }
  if (localWakeUpFlag) {
      handleWakeUp(currentTime);
      // <<< MODIFIED v4.4: Enter waiting state immediately after wake-up >>>
      enterState(STATE_WAITING_FOR_RELEASE);
      // Skip further processing in this loop iteration
      return; 
  }

  // <<< MODIFIED v4.4: Do not handle touch input while waiting for release >>>
  // Handle Touch Input regardless of state (except sleep AND waiting for release)
  if (currentSystemState != STATE_SLEEP && currentSystemState != STATE_WAITING_FOR_RELEASE) {
    handleTouchInput(currentTime);
  }

  // Update state machine
  updateSystemState(currentTime);

  // Actions based on current state
  // Only run mode/dimming logic if system is powered on
  if (systemPoweredOn) {
      switch (currentSystemState) {
        case STATE_IDLE:
        case STATE_MODE_ACTIVE:
          handleMode(currentTime);
          handleEnergySave(currentTime);
          break;
        case STATE_DIMMING:
          handleDimming(currentTime, flags.touchState == HIGH);
          // ✅ REMOVED: handleMode(currentTime); - Mode logic bị skip khi DIM
          handleEnergySave(currentTime); 
          break;
        case STATE_WAITING_FOR_RELEASE: // <<< ADDED v4.4: Handle waiting state >>>
          if (digitalRead(touchPin) == LOW) {
            DEBUG_PRINTLN("Touch released after wake-up. Entering IDLE.");
            // Reset touch state variables before entering IDLE
            lastSteadyState = LOW;
            lastFlickerableState = LOW;
            tapCount = 0;
            pendingStateChange = false;
            touchStartTime = 0;
            enterState(STATE_IDLE); // Transition to normal operation
          }
          // Otherwise, do nothing, just wait for release
          break;
        case STATE_WAKE_UP:
          // Momentary state, should transition out quickly
          break;
        case STATE_TRANSITIONING:
          updateTransition(currentTime);
          // Do not run handleMode during transition
          break;
        case STATE_SLEEP:
          // Should not be reached here if wakeUpFlag logic is correct
          break;
      }
  }

  // ✅ ADD premium updates:
  updateLED4Controller(currentTime);
  
  // Update LED fades and blinking (if active)
  // updateFades() now includes the systemPoweredOn check
  if (flags.isBlinking) {
    updateBlink(currentTime);
  } else {
    updateFades(currentTime);
  }

#ifdef DEBUG
  // Optional periodic checks
  systemHealthCheck();
#ifdef DEBUG_MEMORY
  monitorMemory();
#endif
#ifdef DEBUG_FADES
  // debugFadeSystem(); // Can be noisy
#endif
#ifdef DEBUG_TOUCH
    // static unsigned long lastDebugPrint = 0;
    // if (timeDiff(currentTime, lastDebugPrint) > 100) {
    //     lastDebugPrint = currentTime;
    //     debugTouchSensor();
    // }
#endif
#endif

}

//=========================================================
// === PHẦN 4: STATE MACHINE & CORE LOGIC ===
//=========================================================

void enterState(SystemState newState) {
  if (newState == currentSystemState) return;

  SystemState oldState = currentSystemState;
  previousSystemState = currentSystemState;
  currentSystemState = newState;

  DEBUG_STATE_CHANGE(oldState, newState);

  // Actions on exiting previous state
  switch (previousSystemState) {
    case STATE_DIMMING:
      stopDimming();
      break;
    case STATE_WAKE_UP:
      break;
    case STATE_TRANSITIONING:
      // Reset transition variables if exited prematurely
      transitionFromMode = 0;
      transitionToMode = 0;
      break;
  }

  // Actions on entering new state
  switch (newState) {
    case STATE_IDLE:
      // If system is powered on, reset interaction timer
      if (systemPoweredOn) {
          lastInteractionTime = millis();
          // Ensure modes are correctly set up if coming from wake/transition
          if (oldState == STATE_WAKE_UP || oldState == STATE_TRANSITIONING) {
              setupMode(currentMode); // Re-setup mode outside transition
              applyBrightnessToLeds();
          }
      }
      break;
    case STATE_MODE_ACTIVE:
      // Mode logic is handled continuously if systemPoweredOn
      break;
    case STATE_DIMMING:
      // Only start dimming if system is powered on
      if (systemPoweredOn) {
          startDimming();
      } else {
          // Should not enter dimming if system is off, revert state
          DEBUG_PRINTLN("WARN: Tried to enter DIMMING while systemPoweredOn=false. Reverting.");
          enterState(STATE_IDLE); 
      }
      break;
    case STATE_SLEEP:
      // Actual sleep happens in enterDeepSleep()
      break;
    case STATE_WAKE_UP:
      // Handled in updateSystemState
      break;
    case STATE_TRANSITIONING:
      // Transition setup happens in startTransition()
      // Only start transition if system is powered on
      if (systemPoweredOn) {
#ifdef DEBUG
          digitalWrite(DEBUG_PIN_MODE_CHANGE, HIGH);
          delayMicroseconds(10);
          digitalWrite(DEBUG_PIN_MODE_CHANGE, LOW);
#endif
      } else {
          DEBUG_PRINTLN("WARN: Tried to enter TRANSITIONING while systemPoweredOn=false. Reverting.");
          enterState(STATE_IDLE);
      }
      break;
  }
}

void updateSystemState(unsigned long currentTime) {
  // State transitions based on events or time

  switch (currentSystemState) {
    case STATE_SLEEP:
      // Wake up is handled in loop() now
      break;

    case STATE_WAKE_UP:
      // Wait for a short delay after wake-up before going idle
      // System remains off (systemPoweredOn = false)
      if (timeDiff(currentTime, lastInteractionTime) > WAKE_UP_IDLE_DELAY) {
          enterState(STATE_IDLE);
      }
      break;

    case STATE_IDLE:
    case STATE_MODE_ACTIVE:
      // Stay in these states unless an event triggers a change (handled elsewhere)
      break;

    case STATE_DIMMING:
      // Stay in dimming as long as touch is held (checked in handleDimming)
      break;

    case STATE_TRANSITIONING:
      // updateTransition() handles moving to MODE_ACTIVE when done
      break;
  }
}

//=========================================================
// === PHẦN 5: TOUCH INPUT HANDLING ===
//=========================================================

void handleTouchInput(unsigned long currentTime) {

  // <<< REMOVED v4.4: ignoreTouchAfterWakeUp logic is now handled by STATE_WAITING_FOR_RELEASE >>>

  bool touchPressed = false;
  bool touchReleased = false;

  // Debounce logic
  int currentReading = digitalRead(touchPin);

  if (currentReading != lastFlickerableState) {
    lastDebounceTime = currentTime;
    lastFlickerableState = currentReading;
    pendingStateChange = true;
  }

  if (pendingStateChange && timeDiff(currentTime, lastDebounceTime) > DEBOUNCE_DELAY) {
    if (currentReading != lastSteadyState) {
      lastSteadyState = currentReading;
      pendingStateChange = false;

      if (lastSteadyState == HIGH) { // Touched (Active HIGH)
        touchPressed = true;
        flags.touchState = HIGH; 
        touchStartTime = currentTime;
        // Only update lastInteractionTime if system is already ON
        if (systemPoweredOn) {
            lastInteractionTime = currentTime; 
        }
        DEBUG_PRINTLN("Touch Pressed (HIGH)");
#ifdef DEBUG
        digitalWrite(DEBUG_PIN_TOUCH_EVENT, HIGH);
        delayMicroseconds(10);
        digitalWrite(DEBUG_PIN_TOUCH_EVENT, LOW);
#endif
      } else { // Released (Active HIGH)
        touchReleased = true;
        flags.touchState = LOW; 
        DEBUG_PRINTLN("Touch Released (LOW)");
#ifdef DEBUG
        digitalWrite(DEBUG_PIN_TOUCH_EVENT, HIGH);
        delayMicroseconds(10);
        digitalWrite(DEBUG_PIN_TOUCH_EVENT, LOW);
#endif
        // If dimming was active, exit dimming state
        if (currentSystemState == STATE_DIMMING) {
            enterState(STATE_IDLE); 
            saveStateToEEPROM(); 
        }
      }
    }
  }

  // Detect multiple taps based on press/release events
  detectMultiTaps(currentTime, touchPressed, touchReleased);

  // Handle holding for dimming or reset
  // Only allow dimming/reset if system is powered on
  if (systemPoweredOn && flags.touchState == HIGH) { // Touched
    unsigned long holdDuration = timeDiff(currentTime, touchStartTime);

    if (currentSystemState != STATE_DIMMING && currentSystemState != STATE_TRANSITIONING && holdDuration >= HOLD_THRESHOLD) {
      if (tapCount <= 1) { // Only enter dimming on first hold, not after taps
        enterState(STATE_DIMMING);
      }
    }
  }
}

void detectMultiTaps(unsigned long currentTime, bool touchPressed, bool touchReleased) {
  // Check for tap timeout
  if (tapCount > 0 && timeDiff(currentTime, lastTapTime) > TAP_TIMEOUT) {
    DEBUG_PRINT_VAR("Tap Timeout. Processing taps: ", tapCount);
    processTapAction(tapCount);
    tapCount = 0;
  }

  // Register a tap on release (state goes from HIGH to LOW)
  if (touchReleased) {
    unsigned long pressDuration = timeDiff(currentTime, touchStartTime);
    if (pressDuration < HOLD_THRESHOLD) {
        tapCount++;
        lastTapTime = currentTime;
        DEBUG_PRINTLN_VAR("Tap registered. Count: ", tapCount);
        
        // <<< MODIFIED v4.1: Removed justWokeUp check >>>
        // Process 1-tap immediately ONLY if system is OFF (initial power on)
        // Process 2-tap and 5-tap immediately if system is ON
        if (!systemPoweredOn && tapCount == 1) {
            DEBUG_PRINTLN("Processing first tap to power ON.");
            processTapAction(tapCount);
            tapCount = 0; // Reset count after power on action
        } else if (systemPoweredOn && (tapCount == 2 || tapCount == 5 || tapCount == 7)) {
          DEBUG_PRINT_VAR(tapCount, " Taps Detected. Processing immediately.");
          processTapAction(tapCount);
          tapCount = 0; // Reset count after processing multi-tap
        }
    } else {
        // Long press, reset tap count
        tapCount = 0; 
    }
  }
}

void processTapAction(uint8_t taps) {
  DEBUG_PRINTLN_VAR("Processing Action for Taps: ", taps);
  switch (taps) {
    case 1:
        // <<< MODIFIED v4.1: Handle first tap ONLY for initial power ON >>>
        // Wake-up power ON is handled directly in handleWakeUp()
        if (!systemPoweredOn) { // Only power on if system is currently OFF
            systemPoweredOn = true;
            DEBUG_PRINTLN("System powered ON by first tap action (initial)");
            // Restore last state with a fade-in effect
            // State should have been read from EEPROM in setup()
            setupMode(currentMode); 
            applyBrightnessToLeds(); // Calculate target states
            // Start fade from 0 to target state
            int initialStates[4];
            for(int i=0; i<4; ++i) initialStates[i] = targetLedState[i]; // Get target state
            for(int i=0; i<4; ++i) ledState[i] = 0; // Set current state to 0
            for(int i=0; i<4; ++i) startFade(i, initialStates[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL);
            
            lastInteractionTime = millis(); // Reset idle timer on power on
        } 
        // Original logic: Only change mode if system is ON and not busy
        else if (currentSystemState != STATE_DIMMING && currentSystemState != STATE_TRANSITIONING) {
            nextMode();
        }
        break;
    case 2:
        // Set systemPoweredOn = false before sleep
        if (systemPoweredOn && currentSystemState != STATE_SLEEP) {
            systemPoweredOn = false; 
            enterDeepSleep();
        }
        break;
    case 5:
        // Soft Reset only works if system is ON
        if (systemPoweredOn) {
            resetToDefault();
            // Reset keeps system ON, blink to confirm
            startBlink(0, 3, 150); 
        }
        break;
    case 7:
        // Factory Reset only works if system is ON
        if (systemPoweredOn) {
            DEBUG_PRINTLN("--- FACTORY RESET TRIGGERED (7 TAPS) ---");
            resetToDefault();
            // Factory reset: 5 blinks + watchdog reset
            startBlink(0, 5, 100); 
            while(flags.isBlinking) { 
                updateBlink(millis()); 
                wdt_reset(); 
            } 
            // Force watchdog reset
            while(1); 
        }
        break;
    default:
      // Ignore other tap counts (e.g., 3, 4, 6, 8+)
      break;
  }
}

//=========================================================
// === PHẦN 6: MODE HANDLING & TRANSITIONS ===
//=========================================================

void nextMode() {
  if (!systemPoweredOn || currentSystemState == STATE_TRANSITIONING) return; 

  byte next = currentMode + 1;
  if (next > 4) {
    next = 1;
  }
  
  // ============================================
  // 🔧 CRITICAL FIX: Grace Period Activation After Mode Change
  // ============================================
  // ✅ Activate grace period after mode change
  gracePeriodActive = true;
  gracePeriodEndTime = millis() + GRACE_PERIOD_DURATION;
  DEBUG_PRINTLN("Grace period activated after mode change");
  
  startTransition(currentMode, next);
}

void startTransition(byte fromMode, byte toMode) {
  if (!systemPoweredOn || currentSystemState == STATE_TRANSITIONING) return; 

  DEBUG_PRINT_VAR("Smart Transition: ", fromMode);
  DEBUG_PRINTLN_VAR(" -> ", toMode);

  transitionFromMode = fromMode;
  transitionToMode = toMode;
  transitionStartTime = millis();
  transitionCurrentPhase = TRANS_FADE_OUT;
  enterState(STATE_TRANSITIONING);

  // ✅ SMART FADE-OUT based on current states
  executeSmartFadeOut(fromMode);
}

// ✅ CRITICAL FIX: executeSmartFadeOut() Implementation
void executeSmartFadeOut(byte fromMode) {
  cancelAllFades(); // Clear any existing fades
  stopBlink();
  
  switch (fromMode) {
    case 1: // From Slow Fade
      // Smart fade-out: preserve current sync level, then fade
      for (int i = 0; i < 3; i++) {
        if (ledState[i] > MIN_BRIGHTNESS + 10) {
          if (enableAdvancedTransitions) {
            // Two-stage: brief hold, then smooth fade
            FadeStage slowFadeOut[2] = {
              {ledState[i], 100, 0},           // Hold current for 100ms
              {MIN_BRIGHTNESS, 500, 1}         // Smooth cubic fade out
            };
            startMultiStageFade(i, slowFadeOut, 2, PRIO_TRANSITION);
          } else {
            startFade(i, MIN_BRIGHTNESS, 600, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
          }
        }
      }
      break;
      
    case 2: // From Breathing  
      // Smart fade-out: preserve current breathing level, then fade
      for (int i = 0; i < 3; i++) {
        int currentLevel = ledState[i]; // Current breathing position
        if (currentLevel > MIN_BRIGHTNESS + 10) {
          if (enableAdvancedTransitions) {
            // Two-stage: hold current briefly, then smooth fade
            FadeStage breathingOut[2] = {
              {currentLevel, 100, 0},      // Hold current for 100ms
              {MIN_BRIGHTNESS, 500, 1}     // Smooth fade out
            };
            startMultiStageFade(i, breathingOut, 2, PRIO_TRANSITION);
          } else {
            startFade(i, MIN_BRIGHTNESS, 500, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
          }
        }
      }
      break;
      
    case 3: // From Rain Drops
      // Only fade out LEDs that are currently active
      if (mode3_dropState != 0 && mode3_activeDropLed >= 0 && mode3_activeDropLed < 3) {
        if (ledState[mode3_activeDropLed] > MIN_BRIGHTNESS + 10) {
          startFade(mode3_activeDropLed, MIN_BRIGHTNESS, 400, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
        }
      }
      // Safety: fade out any other LEDs that might be stuck on
      for (int i = 0; i < 3; i++) {
        if (i != mode3_activeDropLed && ledState[i] > MIN_BRIGHTNESS + 10) {
          startFade(i, MIN_BRIGHTNESS, 400, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
        }
      }
      break;
      
    case 4: // From Pattern Wave
      // Only fade out LEDs that are currently active in the pattern
      if (mode4_currentPatternIndex < NUM_PATTERNS) { 
        for (int i = 0; i < 3; i++) {
          bool ledShouldBeOn = pgm_read_byte(&LED_PATTERNS[mode4_currentPatternIndex][i]) > 0;
          if (ledShouldBeOn && ledState[i] > MIN_BRIGHTNESS + 10) {
            startFade(i, MIN_BRIGHTNESS, 400, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
          }
        }
      } else {
        // Fade all if index is somehow invalid
        for (int i = 0; i < 3; i++) {
          if (ledState[i] > MIN_BRIGHTNESS + 10) {
            startFade(i, MIN_BRIGHTNESS, 400, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
          }
        }
      }
      break;
  }
  
  // LED4 remains unchanged during transitions (independent controller)
}


void updateTransition(unsigned long currentTime) {
  if (!systemPoweredOn) return;
  
  switch (transitionCurrentPhase) {
    case TRANS_FADE_OUT:
      // Wait for fade-out to complete
      if (!areFadesActiveOnLeds123()) {
        DEBUG_PRINTLN("Fade-out complete. Starting fade-in...");
        
        // Update mode
        currentMode = transitionToMode;
        
        // Start intelligent fade-in based on target mode
        startIntelligentFadeIn(transitionToMode);
        
        transitionCurrentPhase = TRANS_FADE_IN;
        transitionStartTime = currentTime; // Reset timer for fade-in
      } else if (timeDiff(currentTime, transitionStartTime) > TRANSITION_TIMEOUT / 2) {
        DEBUG_PRINTLN("WARN: Fade-out timed out! Forcing fade-in.");
        cancelAllFades();
        currentMode = transitionToMode;
        startIntelligentFadeIn(transitionToMode);
        transitionCurrentPhase = TRANS_FADE_IN;
        transitionStartTime = currentTime;
      }
      break;
      
    case TRANS_FADE_IN:
      // Wait for fade-in to complete or timeout
      if (!areFadesActiveOnLeds123() || timeDiff(currentTime, transitionStartTime) > TRANSITION_TIMEOUT / 2) {
        DEBUG_PRINTLN("Transition complete.");
        setupMode(currentMode, true); // Final setup
        saveStateToEEPROM();
        enterState(STATE_MODE_ACTIVE);
        transitionCurrentPhase = TRANS_COMPLETE;
        transitionFromMode = 0;
        transitionToMode = 0;
      }
      break;
      
    case TRANS_COMPLETE:
      // Should not reach here, but safety fallback
      enterState(STATE_MODE_ACTIVE);
      break;
  }
}

// Setup mode, optionally called during a transition
void setupMode(byte mode, bool isTransition) {
  if (!systemPoweredOn && !isTransition) { 
      DEBUG_PRINTLN("setupMode skipped (systemPoweredOn=false)");
      return;
  }
  
  // Don't cancel fades if called during transition (fade-out is handled by startTransition)
  if (!isTransition) {
      cancelAllFades(); 
      stopBlink(); 
  }

  // Reset mode-specific variables
  mode1_currentPhase = M1_IDLE;
  mode2_breathingProgress = 0.0f;
  mode3_dropState = 0;
  mode4_currentPatternIndex = 0;

  // Setup based on the new mode
  int led4Target = ledBrightness * LED4_IDLE_PERCENT;
  switch (mode) {
    case 1:
      // Slow Fade: Start the state machine
      mode1_currentPhase = M1_ANALYZE_CURRENT; // ✅ FIX: Use correct enum
      mode1_phaseStartTime = millis();
      DEBUG_MODE1_STATE_CHANGE(M1_IDLE, M1_ANALYZE_CURRENT);
      // ============================================
      // 🔧 CRITICAL FIX #2: LED4 ONLY setup when NOT transitioning
      // ============================================
      // ✅ LED4 ONLY setup when NOT transitioning
      if (!isTransition && systemPoweredOn) {
          setLED4Background(calculateLED4Level(ledBrightness), false);
      }
      break;
    case 2:
      // Breathing: Start breathing cycle
      mode2_lastUpdateTime = millis();
      // 🔧 FIX: Explicit timing reset
      resetBreathingTiming(millis());
      breathingEnergySaveFactor = 1.0f; // Reset energy factor too
      
      // ============================================
      // 🔧 FINAL FIX #2: setupMode() - Consistent Logic
      // ============================================
      // ✅ CONSISTENT: Use same clean logic for all cases
      if (!isTransition && systemPoweredOn) {
          setLED4Background(calculateLED4Level(ledBrightness), false);
      }
      break;
    case 3:
      // Rain Drops: Start first drop
      mode3_lastDropTime = millis();
      mode3_activeDropLed = random(3); 
      // ✅ CONSISTENT: Use same clean logic for all cases
      if (!isTransition && systemPoweredOn) {
          setLED4Background(calculateLED4Level(ledBrightness), false);
      }
      break;
    case 4:
      // Pattern Wave: Start first pattern
      mode4_lastPatternTime = millis();
      // ✅ CONSISTENT: Use same clean logic for all cases
      if (!isTransition && systemPoweredOn) {
          setLED4Background(calculateLED4Level(ledBrightness), false);
      }
      break;
  }
}

// THÊM FUNCTION MỚI: Intelligent fade-in for different modes
void startIntelligentFadeIn(byte toMode) {
  DEBUG_PRINTLN_VAR("Starting intelligent fade-in for mode: ", toMode);
  
  switch (toMode) {
    case 1: // Slow Fade
      // Fade LEDs 1-3 to different starting levels for sync effect
      startFade(0, MIN_BRIGHTNESS + random(50), 800, PRIO_TRANSITION);
      startFade(1, MIN_BRIGHTNESS + random(50), 800, PRIO_TRANSITION);
      startFade(2, MIN_BRIGHTNESS + random(50), 800, PRIO_TRANSITION);
      break;
      
    case 2: // Breathing
      // ============================================
      // 🔧 CRITICAL FIX #4: Calculate breathing phase from current LED states
      // ============================================
      // ✅ Calculate breathing phase from current LED states
      int avgCurrentBrightness = (ledState[0] + ledState[1] + ledState[2]) / 3;
      float currentRatio = (float)(avgCurrentBrightness - MIN_BRIGHTNESS) / (ledBrightness - MIN_BRIGHTNESS);
      currentRatio = constrain(currentRatio, 0.0f, 1.0f);
      
      // Set breathing progress to match current state
      mode2_breathingProgress = acos(1.0f - 2.0f * currentRatio) / (2.0f * PI);
      mode2_breathingStartTime = millis();
      mode2_lastBreathingUpdate = millis();
      mode2_needsTimingReset = false;
      
      // No fade needed - breathing will continue from current position
      break;
      
    case 3: // Rain Drops
      // Start all LEDs 1-3 at MIN, first drop will be triggered by mode logic
      for (int i = 0; i < 3; i++) {
        startFade(i, MIN_BRIGHTNESS, 400, PRIO_TRANSITION);
      }
      break;
      
    case 4: // Pattern Wave
      // Start with first pattern immediately visible
      for (int i = 0; i < 3; i++) {
        bool ledOn = pgm_read_byte(&LED_PATTERNS[0][i]) > 0;
        int target = ledOn ? ledBrightness : MIN_BRIGHTNESS;
        startFade(i, target, 500, PRIO_TRANSITION);
      }
      break;
  }
  
  // LED4 maintains its current brightness (managed by setupMode later)
}

void handleMode(unsigned long currentTime) {
    if (!systemPoweredOn) return; // Do nothing if system hasn't been turned on by touch
    
    // ✅ CRITICAL FIX: Không chạy mode logic khi đang DIM
    if (currentSystemState == STATE_DIMMING) {
        DEBUG_PRINTLN("Skipping mode logic - DIM active");
        return; // Skip mode logic completely during dimming
    }
    
    // Do not run mode logic during transitions
    if (currentSystemState == STATE_TRANSITIONING) return;
    
    // Run mode logic based on currentMode
    switch (currentMode) {
        case 1: handleMode1_SlowFade(currentTime); break;
        case 2: handleMode2_Breathing(currentTime); break;
        case 3: handleMode3_RainDrops(currentTime); break;
        case 4: handleMode4_PatternWave(currentTime); break;
    }
}

// ✅ COMPLETELY REWRITTEN: Mode 1 Enhanced Multi-Phase Logic
void handleMode1_SlowFade(unsigned long currentTime) {
  if (!systemPoweredOn) return;
  
  unsigned long phaseElapsed = timeDiff(currentTime, mode1_phaseStartTime);
  
  switch (mode1_currentPhase) {
    case M1_IDLE:
      // Start the enhanced cycle
      mode1_currentPhase = M1_ANALYZE_CURRENT;
      mode1_phaseStartTime = currentTime;
      DEBUG_MODE1_STATE_CHANGE(M1_IDLE, M1_ANALYZE_CURRENT);
      break;
      
    case M1_ANALYZE_CURRENT:
      // Analyze current LED1-3 states and find sync target
      {
        int maxBrightness = MIN_BRIGHTNESS;
        bool needsSync = false;
        
        // Find brightest LED among 1-3
        for (int i = 0; i < 3; i++) {
          maxBrightness = max(maxBrightness, ledState[i]);
        }
        
        // Check if sync is needed  
        for (int i = 0; i < 3; i++) {
          if (ledState[i] < maxBrightness - 5) { // 5-point tolerance
            needsSync = true;
            break;
          }
        }
        
        if (needsSync) {
          // Start sync phase
          for (int i = 0; i < 3; i++) {
            if (ledState[i] < maxBrightness - 5) {
              startFade(i, maxBrightness, 800, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
            }
          }
          mode1_currentPhase = M1_SYNC_TO_MAX;
          DEBUG_PRINTLN("Mode1: Syncing LEDs to max brightness");
        } else {
          // Skip to fade to target
          mode1_currentPhase = M1_FADE_TO_TARGET;
          DEBUG_PRINTLN("Mode1: LEDs already synced, jumping to target fade");
        }
        mode1_phaseStartTime = currentTime;
      }
      break;
      
    case M1_SYNC_TO_MAX:
      // Wait for sync to complete
      if (!areFadesActiveOnLeds123() || phaseElapsed > 1200) {
        mode1_currentPhase = M1_FADE_TO_TARGET;
        mode1_phaseStartTime = currentTime;
        DEBUG_MODE1_STATE_CHANGE(M1_SYNC_TO_MAX, M1_FADE_TO_TARGET);
      }
      break;
      
    case M1_FADE_TO_TARGET:
      // ============================================
      // 🔧 CRITICAL FIX #1: Mode 1 Phase 2 - Always Fade to 100%
      // ============================================
      // ✅ CORRECT: Phase 2 - Always fade to 100%
      {
        int targetBrightness = MAX_BRIGHTNESS; // ✅ Always 100%, ignore EEPROM in Phase 2
        
        bool fadeStarted = false;
        for (int i = 0; i < 3; i++) {
          if (abs(ledState[i] - targetBrightness) > 5) {
            startFade(i, targetBrightness, 1000, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
            fadeStarted = true;
          }
        }
        
        mode1_currentPhase = M1_HOLD_AT_TARGET;
        mode1_phaseStartTime = currentTime;
        DEBUG_PRINTLN("Mode1: Fading to 100% brightness (Phase 2)");
      }
      break;
      
    case M1_HOLD_AT_TARGET:
      // Hold for 10 seconds
      if (phaseElapsed >= 10000) {
        mode1_currentPhase = M1_FADE_TO_FINAL;
        mode1_phaseStartTime = currentTime;
        DEBUG_MODE1_STATE_CHANGE(M1_HOLD_AT_TARGET, M1_FADE_TO_FINAL);
      }
      break;
      
    case M1_FADE_TO_FINAL:
      // ============================================
      // 🔧 CRITICAL FIX #1: Mode 1 Final Target Logic CORRECT
      // ============================================
      // Fade to final settle brightness with corrected logic
      {
        int finalTarget;
        if (!brightProfile.hasValidEEPROM) {
          finalTarget = (MAX_BRIGHTNESS * 90) / 100; // No EEPROM → 90%
        } else {
          int savedBrightness = brightProfile.userSetBrightness;
          if (savedBrightness > (MAX_BRIGHTNESS * 90) / 100) {
            finalTarget = (MAX_BRIGHTNESS * 90) / 100; // Force 90% if > 90%
          } else {
            finalTarget = savedBrightness; // Keep saved level if ≤ 90%
          }
        }
        
        // Create multi-stage fade for ultra smoothness
        if (enableMultiStageFades) {
          FadeStage finalStages[2] = {
            {finalTarget, 1200, 1},  // 1.2s cubic easing
            {finalTarget, 200, 2}    // 0.2s sine settle
          };
          
          for (int i = 0; i < 3; i++) {
            startMultiStageFade(i, finalStages, 2, PRIO_TRANSITION);
          }
        } else {
          // Fallback for low memory
          for (int i = 0; i < 3; i++) {
            startFade(i, finalTarget, 1200, PRIO_TRANSITION, FADE_GAMMA_SMOOTH);
          }
        }
        
        mode1_currentPhase = M1_SETTLED;
        mode1_phaseStartTime = currentTime;
        DEBUG_PRINTLN_VAR("Mode1: Ultra-smooth fade to final: ", finalTarget);
      }
      break;
      
    case M1_SETTLED:
      // Check if settled, then restart cycle
      if (!areFadesActiveOnLeds123() || phaseElapsed > 2000) {
        mode1_currentPhase = M1_ANALYZE_CURRENT; // Restart cycle
        mode1_phaseStartTime = currentTime;
        DEBUG_MODE1_STATE_CHANGE(M1_SETTLED, M1_ANALYZE_CURRENT);
      }
      break;
  }
  
  // LED4 is handled independently by LED4Controller system
}

/// 🔧 FIX 2: IMPROVED BREATHING CALCULATION (NO DRIFT)
void handleMode2_Breathing(unsigned long currentTime) {
  if (!systemPoweredOn) return;
  
  // 🔧 FIX: Use global variables instead of static
  // Check if timing needs reset
  if (mode2_needsTimingReset || currentTime < mode2_breathingStartTime || mode2_breathingStartTime == 0) {
    resetBreathingTiming(currentTime);
  }
  
  // Control update interval
  if (timeDiff(currentTime, mode2_lastBreathingUpdate) < BREATHING_UPDATE_INTERVAL) {
    return; // Skip this update cycle
  }
  mode2_lastBreathingUpdate = currentTime;

  // Calculate breathing progress from global start time
  unsigned long elapsedTime = timeDiff(currentTime, mode2_breathingStartTime);
  float cycleProgress = fmod((float)elapsedTime / (float)MODE2_BREATH_CYCLE, 1.0f);
  
  float angle = cycleProgress * 2.0f * PI;
  float breathFactor = (cos(angle) * -1.0f + 1.0f) / 2.0f; // 0.0 to 1.0
  
  // 🔧 FIX: Apply energy saving factor to breathing
  int baseBrightness = ledBrightness;
  int targetBrightness = MIN_BRIGHTNESS + (int)((baseBrightness - MIN_BRIGHTNESS) * breathFactor * breathingEnergySaveFactor);
  targetBrightness = constrain(targetBrightness, MIN_BRIGHTNESS, MAX_BRIGHTNESS);

  // Apply directly to LEDs 1-3
  for (int i = 0; i < 3; i++) {
    ledState[i] = targetBrightness;
    analogWrite(ledPins[i], gammaCorrect(targetBrightness));
    targetLedState[i] = targetBrightness;
  }
  
  // ✅ LED4 CONTROLLER INTEGRATION: Use setLED4Background for breathing mode
  if (currentSystemState != STATE_DIMMING) {
    int led4Target = max(MIN_BRIGHTNESS, (int)(ledBrightness * LED4_IDLE_PERCENT * breathingEnergySaveFactor));
    setLED4Background(led4Target, true); // Use immediate update for breathing smoothness
    targetLedState[3] = led4Target;
  } else {
    targetLedState[3] = ledState[3]; 
  }
}


// ============================================
// 🔧 CRITICAL FIX #2: Rain Drops - Actually Turn OFF Non-Participating LEDs
// ============================================
void handleMode3_RainDrops(unsigned long currentTime) {
  if (!systemPoweredOn) return;

  if (mode3_dropState == 0) { // Idle, waiting to start a drop
    if (timeDiff(currentTime, mode3_lastDropTime) > MODE3_DROP_INTERVAL) {
      mode3_lastDropTime = currentTime;
      mode3_activeDropLed = random(3); // Choose LED 0, 1, or 2
      startFade(mode3_activeDropLed, ledBrightness, MODE3_DROP_INTERVAL / 2, PRIO_DEFAULT);
      mode3_dropState = 1; // Now fading in
      DEBUG_PRINTLN_VAR("Raindrop Start LED: ", mode3_activeDropLed);
    }
  } else if (mode3_dropState == 1) { // Fading In
    if (!fades[mode3_activeDropLed].active) { // Fade in complete
      // ============================================
      // 🔧 FINAL FIX #1: Rain Drops - Active LED Fade to 0
      // ============================================
      // ✅ CORRECT: Fade to 0, not MIN_BRIGHTNESS
      startFade(mode3_activeDropLed, 0, MODE3_DROP_INTERVAL / 2, PRIO_DEFAULT);
      mode3_dropState = 2; // Now fading out
      DEBUG_PRINTLN_VAR("Raindrop Fade Out LED: ", mode3_activeDropLed);
    }
  } else if (mode3_dropState == 2) { // Fading Out
    if (!fades[mode3_activeDropLed].active) { // Fade out complete
      mode3_dropState = 0; // Back to idle
      DEBUG_PRINTLN_VAR("Raindrop Complete LED: ", mode3_activeDropLed);
    }
  }

  // ✅ Set target states: active LED = bright, others = OFF
  for(int i = 0; i < 3; i++) {
      if (mode3_dropState != 0 && i == mode3_activeDropLed) {
          targetLedState[i] = ledBrightness; // Active LED
      } else {
          targetLedState[i] = 0; // ✅ Completely OFF
      }
  }

  // ✅ CRITICAL ADD: Actually turn OFF non-participating LEDs
  for(int i = 0; i < 3; i++) {
      if (mode3_dropState == 0 || i != mode3_activeDropLed) {
          // Only start fade if LED is currently ON
          if (ledState[i] > MIN_BRIGHTNESS) {
              startFade(i, 0, 400, PRIO_DEFAULT); // ✅ Actually turn OFF
          }
      }
  }

  // ✅ LED4 CONTROLLER INTEGRATION: Use setLED4Background instead of startFade
  if (currentSystemState != STATE_DIMMING) {
      setLED4Background(calculateLED4Level(ledBrightness), false);
      targetLedState[3] = calculateLED4Level(ledBrightness);
  }
}

// ============================================
// 🔧 CRITICAL FIX #2: Pattern Wave - Actually Turn OFF Non-Participating LEDs
// ============================================
void handleMode4_PatternWave(unsigned long currentTime) {
  if (!systemPoweredOn) return;

  if (timeDiff(currentTime, mode4_lastPatternTime) > MODE4_PATTERN_DURATION) {
    mode4_lastPatternTime = currentTime;
    mode4_currentPatternIndex = (mode4_currentPatternIndex + 1) % NUM_PATTERNS;

    DEBUG_PRINTLN_VAR("Pattern Wave Index: ", mode4_currentPatternIndex);

    // Only apply pattern to LEDs 1-3
    for (int i = 0; i < 3; i++) {
      if (mode4_currentPatternIndex < NUM_PATTERNS) { 
          bool ledOn = pgm_read_byte(&LED_PATTERNS[mode4_currentPatternIndex][i]) > 0;
          int target = ledOn ? ledBrightness : 0; // ✅ 0 instead of MIN_BRIGHTNESS
          
          // ✅ CRITICAL ADD: Always start fade for pattern changes
          if (!fades[i].active || fades[i].priority <= PRIO_PATTERN) {
              startFade(i, target, MODE4_PATTERN_DURATION / 2, PRIO_PATTERN);
          }
          targetLedState[i] = target;
      }
    }
  }
  
  // ✅ LED4 CONTROLLER INTEGRATION: Use setLED4Background instead of startFade
  if (currentSystemState != STATE_DIMMING) {
      setLED4Background(calculateLED4Level(ledBrightness), false);
      targetLedState[3] = calculateLED4Level(ledBrightness);
  }
}

//=========================================================
// === PHẦN 7: FADE ENGINE ===
//=========================================================

// ============================================
// 🔧 CRITICAL FIX #6: Remove Duplicate startFade() Implementation  
// ============================================
// Duplicate wrapper function removed - using default parameters instead

// Helper to start fades only on LEDs 1-3
void startLeds123FadeTo(int toVal, unsigned long durVal, uint8_t priority) {
  if (!systemPoweredOn && priority < PRIO_CRITICAL) return;
  
  for (int i = 0; i < 3; i++) {
    startFade(i, toVal, durVal, priority);
  }
}

// ✅ ULTRA-SMOOTH 40ms TIMING SYSTEM
void updateFades(unsigned long currentTime) {
    if (flags.isBlinking) return; // Don't update fades while blinking

    // ✅ PREMIUM TIMING: Only update every 40ms for ultra-smooth experience (25fps)
    static unsigned long lastUltraUpdate = 0;
    if (enableUltraSmoothing && timeDiff(currentTime, lastUltraUpdate) < ULTRA_SMOOTH_INTERVAL) {
        return;
    }
    lastUltraUpdate = currentTime;

    if (!systemPoweredOn) {
        // Ensure all LEDs are physically off if system isn't powered on
        bool changed = false;
        for (int i = 0; i < 4; i++) {
             if (ledState[i] != 0) {
                 ledState[i] = 0; // Update state variable if needed
                 changed = true;
             }
             // Ensure hardware is off regardless of state variable
             analogWrite(ledPins[i], 0); 
        }
        return; // Don't process fades
    }

    // --- Original logic continues below --- 
    for (int i = 0; i < 4; i++) {
        // Special handling during dimming: LED4 is controlled by dimming logic
        if (currentSystemState == STATE_DIMMING && i == 3) {
            // Let handleDimming control LED4 via startFade
            // We still need to process the fade set by handleDimming here
        } 
        // Special handling during dimming: LEDs 1-3 continue their mode logic
        else if (currentSystemState == STATE_DIMMING && i < 3) {
            // Mode logic continues to call startFade for LEDs 1-3
            // Process the fade normally here
        } 
        // Normal operation or non-dimming state
        else {
            // Process fades normally
        }

        if (fades[i].active) {
            unsigned long elapsedTime = timeDiff(currentTime, fades[i].start_time_ms);
            float progress = 1.0f;
            if (fades[i].duration_ms > 0) {
                progress = (float)elapsedTime / fades[i].duration_ms;
            }
            
            if (progress >= 1.0f) {
                // ✅ FIX: Multi-stage transition logic
                if (fades[i].fade_type == FADE_SMART_CONTEXT && 
                    fades[i].current_stage < fades[i].stage_count - 1) {
                    
                    // Move to next stage
                    fades[i].current_stage++;
                    fades[i].from_val = fades[i].to_val; // Current becomes new start
                    fades[i].to_val = fades[i].stages[fades[i].current_stage].target_val;
                    fades[i].duration_ms = fades[i].stages[fades[i].current_stage].duration_ms;
                    fades[i].start_time_ms = currentTime;
                    
                    // Continue processing this fade in the same loop iteration
                    elapsedTime = 0;
                    progress = 0.0f;
                } else {
                    // Fade complete
                    ledState[i] = fades[i].to_val;
                    fades[i].active = false;
                    continue; // Skip to next LED
                }
            }
            
            if (fades[i].active) { // Still active after stage check
                // ✅ Enhanced easing with fadeType support
                float easedProgress;
                
                // Use enhanced easing system
                if (fades[i].fade_type != FADE_LINEAR) {
                    easedProgress = calculateEasedProgress(progress, fades[i].fade_type, fades[i].current_stage);
                } else {
                    // Legacy breathing mode optimization
                    if (currentMode == 2 && i < 3 && fades[i].priority <= PRIO_DEFAULT) {
                        easedProgress = progress; // Linear for breathing
                    } else {
                        easedProgress = easeInOutCubic(progress); // Default smooth
                    }
                }
                
                ledState[i] = fades[i].from_val + (fades[i].to_val - fades[i].from_val) * easedProgress;
            }
        }
        // Apply gamma correction and write to PWM pin
        // Ensure minimum brightness is applied if target is MIN_BRIGHTNESS
        int outputValue = ledState[i];
        if (outputValue < MIN_BRIGHTNESS && outputValue > 0) {
            // If the state is between 1 and MIN_BRIGHTNESS-1, output MIN_BRIGHTNESS
            // This prevents the LED from turning completely off unless the target is 0
            // Exception: Allow fade to 0 during sleep fade
            if (!(fades[i].active && fades[i].to_val == 0 && fades[i].priority == PRIO_CRITICAL)) {
                 outputValue = MIN_BRIGHTNESS;
            }
        }
        analogWrite(ledPins[i], gammaCorrect(outputValue));
    }
}

void cancelFade(int ledIndex) {
  if (ledIndex >= 0 && ledIndex < 4) {
    fades[ledIndex].active = false;
  }
}

void cancelAllFades() {
  for (int i = 0; i < 4; i++) {
    fades[i].active = false;
  }
}

// Check if any fades are active, optionally excluding LED4
bool areFadesActive(bool includeLed4) {
  if (!systemPoweredOn) return false;
  
  int limit = includeLed4 ? 4 : 3;
  for (int i = 0; i < limit; i++) {
    if (fades[i].active) return true;
  }
  return false;
}

// Specific check for LEDs 1-3 (used in transitions and Mode 1)
bool areFadesActiveOnLeds123() {
    if (!systemPoweredOn) return false;
    return areFadesActive(false);
}

//=========================================================
// === PHẦN 8: DIMMING LOGIC (LED4 Only) ===
//=========================================================

// ============================================
// 🔧 CRITICAL DIMMING FIX: Complete startDimming() Rewrite
// ============================================
void startDimming() {
  if (!systemPoweredOn || flags.dimActive) return;

  DEBUG_PRINTLN("FIXED Dimming Start: Smart Direction & Non-blocking Pre-fade");
  
  // ✅ CORRECTED DIRECTION LOGIC: <50% up, 50-79% up, ≥80% down
  float brightnessPercent = (float)ledBrightness / MAX_BRIGHTNESS * 100.0f;
  
  if (brightnessPercent < 50.0f) {
    dimTowardsMin = false;  // ✅ CHANGE: < 50% → UP
    dimTargetBrightness = MAX_BRIGHTNESS;
    DEBUG_PRINTLN("Direction: UP to MAX (< 50%)");
  } else if (brightnessPercent >= 50.0f && brightnessPercent < 80.0f) {
    dimTowardsMin = false;  
    dimTargetBrightness = MAX_BRIGHTNESS;
    DEBUG_PRINTLN("Direction: UP to MAX (50-79%)");
  } else {
    dimTowardsMin = true;
    dimTargetBrightness = DIM_MIN_BRIGHTNESS;  // ✅ CHANGE: Use DIM_MIN_BRIGHTNESS
    DEBUG_PRINTLN("Direction: DOWN to 10% (≥ 80%)");
  }

  // ✅ NON-BLOCKING PRE-FADE CHECK
  bool needsPreFade = false;
  for (int i = 0; i < 4; i++) {
    if (ledState[i] < MIN_BRIGHTNESS) {
      needsPreFade = true;
      break;
    }
  }

  // ✅ STATE MACHINE INITIALIZATION
  dimState = needsPreFade ? DIM_PRE_FADE : DIM_READY;
  dimStateStartTime = millis();
  dimStartBrightness = ledBrightness;
  dimLed4TargetBrightness = ledBrightness; // Start from current
  
  if (needsPreFade) {
    DEBUG_PRINTLN("Pre-fade: Starting NON-BLOCKING fade for OFF LEDs");
    // ✅ NO BLOCKING delay() - use fade engine instead
    for (int i = 0; i < 4; i++) {
      if (ledState[i] < MIN_BRIGHTNESS) {
        int targetLevel = (i == 3) ? calculateLED4Level(ledBrightness) : ledBrightness;
        startFade(i, targetLevel, DIM_PRE_FADE_DURATION, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
      }
    }
  }

  flags.dimActive = true;
  cancelFade(3); // Cancel LED4's current fade for dimming control
  stopBlink(); // Stop blinking if active
  lastDimUpdate = millis();
  
  DEBUG_PRINT_VAR("Dimming initialized: Start=", dimStartBrightness);
  DEBUG_PRINT_VAR(", Target=", dimTargetBrightness);
  DEBUG_PRINTLN_VAR(", TowardsMin=", dimTowardsMin);
}

void stopDimming() {
  if (!flags.dimActive) return;

  DEBUG_PRINTLN("Stop Dimming - LED4 maintains chosen brightness");
  flags.dimActive = false;
  
  // ✅ CRITICAL FIX: LED4 giữ nguyên brightness được chọn
  led4ChosenBrightness = dimLed4TargetBrightness;
  DEBUG_PRINTLN_VAR("LED4 chosen brightness saved: ", led4ChosenBrightness);
  
  // ❌ REMOVED: Không restore LED4 về LED4_IDLE_PERCENT nữa!
  // ❌ REMOVED: Không fade LED4 nữa!
  // ✅ LED4 maintains exactly what user chose

  // ============================================
  // 🔧 CRITICAL FIX: Grace Period Activation After Dimming
  // ============================================
  // ✅ Activate grace period after dimming
  gracePeriodActive = true;
  gracePeriodEndTime = millis() + GRACE_PERIOD_DURATION;
  DEBUG_PRINTLN("Grace period activated after dimming");
  
  lastInteractionTime = millis(); 
}

// ============================================
// 🔧 FINAL CRITICAL DIMMING FIX: Continuous User Control
// ============================================
void handleDimming(unsigned long currentTime, bool touchHeld) {
  if (!systemPoweredOn || !flags.dimActive) return;

  // ✅ CRITICAL FIX: Handle touch release - Apply to ALL LEDs consistently
  if (!touchHeld) {
    DEBUG_PRINTLN("Touch released - applying final brightness to ALL LEDs");
    
    // ✅ Save CURRENT brightness (what user sees now)
    ledBrightness = dimLed4TargetBrightness;
    
    // ✅ CRITICAL: Apply final brightness to ALL LEDs consistently
    applyDimmingToAllLEDs(dimLed4TargetBrightness);
    
    // ✅ Update brightness profile
    onBrightnessChanged(dimLed4TargetBrightness);
    
    stopDimming();
    enterState(STATE_IDLE);
    saveStateToEEPROM();
    return;
  }

  // ✅ State machine for pre-fade and active dimming
  switch (dimState) {
    case DIM_PRE_FADE:
      // Wait for pre-fade to complete, then move to ready
      if (timeDiff(currentTime, dimStateStartTime) >= DIM_PRE_FADE_DURATION) {
        DEBUG_PRINTLN("Pre-fade complete. Ready for dimming.");
        dimState = DIM_ACTIVE;
        dimStateStartTime = currentTime;
        dimLed4TargetBrightness = dimStartBrightness; // Start from initial brightness
      }
      break;
      
    case DIM_READY:
      // Start active dimming immediately
      dimState = DIM_ACTIVE;
      dimStateStartTime = currentTime;
      DEBUG_PRINTLN("Starting active dimming");
      break;
      
    case DIM_ACTIVE:
      // ✅ CONTINUOUS MOVEMENT với DYNAMIC STEP SIZE!
      if (timeDiff(currentTime, lastDimUpdate) >= DIM_INTERVAL) {
        lastDimUpdate = currentTime;
        
        // ✅ Calculate dynamic step based on current brightness
        int dynamicStep = calculateDimStep(dimLed4TargetBrightness);
        
        // ✅ Move continuously in chosen direction
        if (dimTowardsMin) {
          dimLed4TargetBrightness -= dynamicStep;
          dimLed4TargetBrightness = max(DIM_MIN_BRIGHTNESS, dimLed4TargetBrightness);
        } else {
          dimLed4TargetBrightness += dynamicStep;
          dimLed4TargetBrightness = min(MAX_BRIGHTNESS, dimLed4TargetBrightness);
        }
        
        // ✅ Apply to all LEDs immediately
        applyDimmingToAllLEDs(dimLed4TargetBrightness);
        
        DEBUG_PRINT_VAR("Dynamic dimming step ", dynamicStep);
        DEBUG_PRINTLN_VAR(", new brightness: ", dimLed4TargetBrightness);
        
        // ✅ NO STOPPING LOGIC - Continue until user releases
      }
      break;
      
    case DIM_FINISHING:
      // Should not reach here during active dimming
      break;
      
    default:
      // Safety fallback
      dimState = DIM_ACTIVE;
      break;
  }
}

// ============================================
// 🔧 BƯỚC 2: REDESIGNED CORE DIM LOGIC - MODE-SPECIFIC DIMMING
// ============================================
// ============================================
// 🔧 FINAL CRITICAL FIX: Target States Update for Future Mode Switches
// ============================================
void updateTargetStatesAfterDim(int newBrightness) {
  if (!systemPoweredOn || !flags.dimActive) return;
  
  DEBUG_PRINTLN_VAR("Updating target states after dim to: ", newBrightness);
  
  // ✅ CRITICAL: Update brightness cho TẤT CẢ LEDs để đảm bảo consistency
  switch (currentMode) {
    case 1:  // Slow Fade - all LEDs use same target
      for (int i = 0; i < 4; i++) {
        targetLedState[i] = newBrightness;
      }
      break;
      
    case 2:  // Breathing - Smart update
    case 3:  // Rain Drops
    case 4:  // Pattern Wave
      for (int i = 0; i < 4; i++) {
        if (i == 3) {
          // LED4: Luôn được update brightness
          targetLedState[i] = newBrightness;
        } else {
          // LEDs 1-3: Smart update based on current state
          if (targetLedState[i] > 0) {
            // Currently ON LED → Update brightness immediately
            targetLedState[i] = newBrightness;
          } else {
            // ✅ CRITICAL FIX: OFF LEDs cũng lưu brightness cho future mode switch
            // Khi switch sang Mode 1, sẽ dùng brightness này
            targetLedState[i] = newBrightness;
            DEBUG_PRINTLN_VAR("Updated OFF LED", i);
            DEBUG_PRINTLN(" brightness for future mode switch");
          }
        }
      }
      break;
  }
  
  // ✅ Ensure global brightness is updated
  ledBrightness = newBrightness;
  
  DEBUG_PRINTLN("All target states updated with new brightness");
}

#ifdef DEBUG
void testDimModeSwitch() {
  DEBUG_PRINTLN("=== Testing DIM + Mode Switch ===");
  
  // Start in Mode 3 (Rain Drops)
  currentMode = 3;
  setupMode(3);
  
  // Simulate DIM to 150
  flags.dimActive = true;
  updateTargetStatesAfterDim(150);
  
  // Verify LEDs 1-3 có brightness 150 even if OFF
  for (int i = 0; i < 3; i++) {
    DEBUG_PRINT_VAR("LED", i);
    DEBUG_PRINTLN_VAR(" target after DIM: ", targetLedState[i]);
  }
  
  // Switch to Mode 1
  currentMode = 1;
  setupMode(1);
  
  // Verify tất cả LEDs có brightness đúng
  for (int i = 0; i < 4; i++) {
    DEBUG_PRINT_VAR("LED", i);
    DEBUG_PRINTLN_VAR(" target in Mode 1: ", targetLedState[i]);
  }
  
  DEBUG_PRINTLN("=== Test Complete ===");
}
#endif

void applyDimmingToAllLEDs(int newBrightness) {
  if (!systemPoweredOn || !flags.dimActive) return;
  
  DEBUG_PRINT_VAR("Smart dimming to brightness: ", newBrightness);
  
  // Update target states first
  updateTargetStatesAfterDim(newBrightness);
  
  // ✅ MODE-SPECIFIC DIMMING LOGIC
  switch (currentMode) {
    case 1: // Slow Fade: Tất cả LEDs phải sáng
      {
        DEBUG_PRINTLN(" - Mode 1: All LEDs dimmed together");
        for (int i = 0; i < 4; i++) {
          ledState[i] = newBrightness;
          analogWrite(ledPins[i], gammaCorrect(newBrightness));
        }
      }
      break;
      
    case 2: // Breathing: Smart dimming
    case 3: // Rain Drops: Smart dimming  
    case 4: // Pattern Wave: Smart dimming
      {
        DEBUG_PRINTLN(" - Mode 2/3/4: Smart dimming (ON LEDs + LED4)");
        for (int i = 0; i < 4; i++) {
          if (i == 3) {
            // LED4: Luôn được dim
            ledState[i] = newBrightness;
          } else {
            // LEDs 1-3: Chỉ dim nếu đang sáng (> DIM_MIN_BRIGHTNESS)
            if (ledState[i] > DIM_MIN_BRIGHTNESS) {
              ledState[i] = newBrightness;
              DEBUG_PRINT_VAR(" - LED", i);
              DEBUG_PRINTLN(" dimmed (was ON)");
            } else {
              DEBUG_PRINT_VAR(" - LED", i);
              DEBUG_PRINTLN(" kept OFF");
            }
          }
          analogWrite(ledPins[i], gammaCorrect(ledState[i]));
        }
      }
      break;
  }
  
  DEBUG_PRINTLN(" - Real-time dimming applied");
}

// Helper function to check if LED is currently considered "ON"
bool isLedCurrentlyOn(int ledIndex) {
  if (ledIndex < 0 || ledIndex >= 4) return false;
  
  switch (currentMode) {
    case 1: // Slow Fade: Tất cả LEDs 1,2,3 luôn ON
      return (ledIndex < 4); // All LEDs should be ON in mode 1
      
    case 2: // Breathing: Check actual brightness
    case 3: // Rain Drops: Check actual brightness
    case 4: // Pattern Wave: Check actual brightness
      if (ledIndex == 3) return true; // LED4 always participates
      return (ledState[ledIndex] > DIM_MIN_BRIGHTNESS);
      
    default:
      return false;
  }
}

//=========================================================
// === PHẦN 9: BLINKING LOGIC ===
//=========================================================

void startBlink(int ledIndex, int times, unsigned long interval) {
  if (!systemPoweredOn) return;
  
  if (ledIndex < 0 || ledIndex >= 4 || times <= 0) return;

  stopBlink(); // Stop any previous blink
  cancelFade(ledIndex); // Cancel any fade on the target LED

  flags.isBlinking = true;
  blinkPinIndex = ledIndex;
  blinkTimesTotal = times * 2; // On and Off counts as 2 steps
  blinkCountCurrent = 0;
  blinkInterval = interval / 2; // Interval for each on/off state
  blinkOriginalState = ledState[ledIndex]; // Save state to restore later
  blinkStartTime = millis();
  blinkIsOn = true; // Start with LED ON

  DEBUG_PRINT_VAR("Start Blink LED: ", ledIndex);
  DEBUG_PRINTLN_VAR(", Times: ", times);

  // Turn LED on immediately (using current brightness)
  analogWrite(ledPins[blinkPinIndex], gammaCorrect(ledBrightness)); 
}

void updateBlink(unsigned long currentTime) {
  if (!flags.isBlinking) return;
  if (!systemPoweredOn) { stopBlink(); return; }

  if (timeDiff(currentTime, blinkStartTime) >= blinkInterval) {
    blinkStartTime = currentTime;
    blinkCountCurrent++;

    if (blinkCountCurrent >= blinkTimesTotal) {
      stopBlink(); // Finished blinking
      return;
    }

    // Toggle LED state
    blinkIsOn = !blinkIsOn; 
    int val = blinkIsOn ? ledBrightness : 0;
    analogWrite(ledPins[blinkPinIndex], gammaCorrect(val));
    // Do not update ledState[blinkPinIndex] here, restore original state in stopBlink
  }
}

void stopBlink() {
  if (!flags.isBlinking) return;
  flags.isBlinking = false;
  // Restore the LED to its state before blinking started
  // Only restore if system is still powered on
  if (systemPoweredOn) {
      startFade(blinkPinIndex, blinkOriginalState, blinkInterval * 2, PRIO_CRITICAL);
  }
  DEBUG_PRINTLN("Stop Blink");
}

//=========================================================
// === PHẦN 10: SLEEP & WAKE LOGIC ===
//=========================================================

// ISR function (v4.6 Checklist Fix - Enhanced Debounce)
void wakeUpISR() {
  static unsigned long lastISRTime = 0;
  unsigned long currentISRTime = millis();

  // Debounce: Ignore if ISR triggered too quickly after the last one
  // Check currentISRTime != 0 to handle potential millis() rollover edge case if ISR happens exactly at rollover
  if (currentISRTime != 0 && (currentISRTime - lastISRTime) < 50) { // 50ms debounce
    return; // Ignore this trigger
  }
  lastISRTime = currentISRTime; // Update time of the last valid trigger

  // Only set flag if touch is actually HIGH (relevant for CHANGE trigger)
  if (digitalRead(touchPin) == HIGH) {
    wakeUpFlag = true;
  }
}

// Called from loop() when wakeUpFlag is detected
void handleWakeUp(unsigned long currentTime) {
  DEBUG_PRINTLN("--- Woke Up & Powering ON --- ");
  lastInteractionTime = currentTime;

  // <<< REMOVED v4.4: ignoreTouchAfterWakeUp flag is no longer used >>>

  // <<< ADDED v4.1: Power ON logic moved here >>>
  systemPoweredOn = true;
  DEBUG_PRINTLN("System powered ON by wake-up action");

  // ✅ Reset LED4 chosen brightness on wake-up
  resetLED4ChosenBrightness();

  // Restore last state (already read in setup or saved before sleep)
  currentMode = savedMode;
  ledBrightness = savedBrightness;
  DEBUG_PRINT_VAR("Restoring State: Mode=", currentMode);
  DEBUG_PRINTLN_VAR(", Brightness=", ledBrightness);

  // Setup the restored mode and apply brightness
  setupMode(currentMode); 
  applyBrightnessToLeds(); // Calculate target states based on restored brightness

  // Start fade from 0 to target state for power-on effect
  int initialStates[4];
  for(int i=0; i<4; ++i) initialStates[i] = targetLedState[i]; // Get target state
  for(int i=0; i<4; ++i) ledState[i] = 0; // Set current state to 0
  for(int i=0; i<4; ++i) startFade(i, initialStates[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL);
  
  // No need to change state here, loop() will handle it after this function returns
}


// Function to enter deep sleep (v4.6 Checklist Fix)
void enterDeepSleep() {
    if (flags.systemSleep) return;

    DEBUG_PRINTLN("--- Entering Deep Sleep (v4.6 Fix) --- ");
#ifdef DEBUG
    Serial.flush();
#endif

    // Save current state before sleeping
    savedMode = currentMode;
    savedBrightness = ledBrightness;
    saveStateToEEPROM(true); // Force save before sleep

    flags.systemSleep = true;
    systemPoweredOn = false;
    enterState(STATE_SLEEP);
    cancelAllFades();
    stopBlink();

    // Only fade LEDs that are currently ON for smooth effect
    DEBUG_PRINTLN("Starting smooth sleep fade for active LEDs...");
    const unsigned long SMOOTH_SLEEP_FADE = 1000; // 1 second as user requested

    for (int i = 0; i < 4; i++) {
      if (ledState[i] > 0) {
        DEBUG_PRINT_VAR("Fading LED", i);
        DEBUG_PRINTLN_VAR(" from brightness: ", ledState[i]);
        startFade(i, 0, SMOOTH_SLEEP_FADE, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
      } else {
        DEBUG_PRINT_VAR("LED", i);
        DEBUG_PRINTLN(" already OFF - no fade needed");
      }
    }

    unsigned long fadeStart = millis();
    DEBUG_PRINTLN("Waiting for smooth sleep fade to complete...");

    while (areFadesActive() && timeDiff(millis(), fadeStart) < SMOOTH_SLEEP_FADE + 200) {
        unsigned long now = millis();
        
        // Update all active fades
        for (int i = 0; i < 4; i++) {
            if (fades[i].active && fades[i].priority == PRIO_CRITICAL) {
                unsigned long elapsedTime = timeDiff(now, fades[i].start_time_ms);
                float progress = (fades[i].duration_ms == 0) ? 1.0f : (float)elapsedTime / fades[i].duration_ms;
                
                if (progress >= 1.0f) {
                    ledState[i] = 0;
                    fades[i].active = false;
                    DEBUG_PRINT_VAR("LED", i);
                    DEBUG_PRINTLN(" fade complete");
                } else {
                    float easedProgress = easeInOutCubic(progress);
                    ledState[i] = fades[i].from_val * (1.0f - easedProgress);
                    analogWrite(ledPins[i], gammaCorrect(ledState[i]));
                }
            }
        }
        
        wdt_reset();
        delay(10); // Small delay for ultra-smooth fade
    }

    DEBUG_PRINTLN("Sleep fade completed - all LEDs off");

    // Ensure LEDs are off
    for (int i = 0; i < 4; i++) {
        analogWrite(ledPins[i], 0);
        ledState[i] = 0;
        targetLedState[i] = 0;
        fades[i].active = false;
    }

    // 🔧 ADD: Proper pin preparation
    pinMode(touchPin, INPUT); // Ensure it's input before checking/sleeping
    delay(100); // Let pin settle

    // 🔧 ADD: Current state check - wait if HIGH
    int currentTouchState = digitalRead(touchPin);
    if (currentTouchState == HIGH) {
        DEBUG_PRINTLN("WARNING: Touch pin HIGH before sleep! Waiting for release...");
        unsigned long waitStart = millis();
        // Wait up to 5 seconds for release
        while (digitalRead(touchPin) == HIGH && timeDiff(millis(), waitStart) < 5000) {
            wdt_reset(); // Keep WDT happy while waiting
            delay(10);   // Small delay to prevent busy-waiting too hard
        }
        // Add a small delay after release before proceeding
        delay(200);
        DEBUG_PRINTLN("Touch released or timeout reached.");
    }

    // Prepare for sleep
    wdt_disable();
    set_sleep_mode(SLEEP_MODE_PWR_DOWN);
    sleep_enable();

    // Ensure wakeUpFlag is clear before attaching interrupt
    ATOMIC_BLOCK(ATOMIC_RESTORESTATE) {
        wakeUpFlag = false;
    }

    // 🔧 ADD: Multiple flag clearing for robustness
    EIFR = bit(INTF0); // Clear INT0 flag
    delay(50);         // Short delay
    EIFR = bit(INTF0); // Clear INT0 flag again
    delay(10);

    // Attach interrupt using CHANGE trigger
    attachInterrupt(digitalPinToInterrupt(touchPin), wakeUpISR, CHANGE);

    // 🔧 ADD: Pre-sleep validation
    bool canSleep = false;
    ATOMIC_BLOCK(ATOMIC_RESTORESTATE) {
        // Re-check wakeUpFlag and ensure touch pin is LOW right before sleeping
        if (!wakeUpFlag && digitalRead(touchPin) == LOW) {
            canSleep = true;
        }
    }

    if (canSleep) {
        DEBUG_PRINTLN("Conditions met. Entering sleep_cpu()...");
        sleep_cpu(); // Enter sleep mode
    } else {
        // If conditions aren't met (e.g., wakeUpFlag set or touch still HIGH), abort sleep
        DEBUG_PRINTLN("Sleep aborted - unsafe conditions detected just before sleep_cpu().");
        // Cleanup: detach interrupt, re-enable WDT, etc.
        detachInterrupt(digitalPinToInterrupt(touchPin));
        wdt_enable(WDTO_2S);
        wdt_reset();
        sleep_disable();
        // Potentially set state back to IDLE or handle error
        // For now, just prevent sleep and let the main loop continue
        // Re-enable system power logic if needed
        // systemPoweredOn = true; // Reconsider if needed based on flow
        // enterState(STATE_IDLE); // Reconsider if needed
        return; // Exit function without sleeping
    }

    // --- CPU WAKES UP HERE ---
    sleep_disable();
    detachInterrupt(digitalPinToInterrupt(touchPin));
    wdt_enable(WDTO_2S); // Re-enable watchdog
    wdt_reset();         // Reset WDT immediately after enabling

    // handleWakeUp() and state transition happen in the main loop
}

//=========================================================
// === PHẦN 11: EEPROM HANDLING ===
//=========================================================

byte calculateCRC8(byte mode, byte brightness) {
  byte crc = 0;
  crc ^= mode;
  for (int i = 0; i < 8; i++) {
    crc = (crc & 0x80) ? (crc << 1) ^ 0x07 : crc << 1;
  }
  crc ^= brightness;
  for (int i = 0; i < 8; i++) {
    crc = (crc & 0x80) ? (crc << 1) ^ 0x07 : crc << 1;
  }
  return crc;
}

void readStateFromEEPROM(byte &modeToSet, byte &brightnessToSet) {
  int validCell = -1;
  byte readMode, readBrightness, readCRC;

  // Find the most recently written valid cell
  for (int offset = 0; offset < EEPROM_CELL_COUNT; ++offset) {
    int currentCellIndex = (eepromNextCellIndex + EEPROM_CELL_COUNT - 1 - offset) % EEPROM_CELL_COUNT;
    
    if (eepromCellRetries[currentCellIndex] >= EEPROM_MAX_RETRIES) {
        DEBUG_PRINT_VAR("Skipping blacklisted cell during read: ", currentCellIndex);
        continue;
    }

    int addr = EEPROM_START_ADDR + currentCellIndex * EEPROM_BLOCK_SIZE;
    readMode = EEPROM.read(addr);
    readBrightness = EEPROM.read(addr + 1);
    readCRC = EEPROM.read(addr + 2);

    if (calculateCRC8(readMode, readBrightness) == readCRC) {
      if (readMode >= 1 && readMode <= 4 && readBrightness >= MIN_BRIGHTNESS && readBrightness <= MAX_BRIGHTNESS) {
          validCell = currentCellIndex;
          eepromCellRetries[validCell] = 0; 
          DEBUG_PRINT_VAR("Valid EEPROM cell found: ", validCell);
          break; 
      } else {
          DEBUG_PRINT_VAR("EEPROM Cell ", currentCellIndex);
          DEBUG_PRINTLN(" CRC OK but data invalid.");
      }
    } else {
        DEBUG_PRINT_VAR("EEPROM Cell ", currentCellIndex);
        DEBUG_PRINTLN(" CRC mismatch.");
    }
  }

  if (validCell != -1) {
    int addr = EEPROM_START_ADDR + validCell * EEPROM_BLOCK_SIZE;
    modeToSet = EEPROM.read(addr);
    brightnessToSet = EEPROM.read(addr + 1);
    eepromNextCellIndex = (validCell + 1) % EEPROM_CELL_COUNT; 
    int initialNext = eepromNextCellIndex;
    while (eepromCellRetries[eepromNextCellIndex] >= EEPROM_MAX_RETRIES) {
        eepromNextCellIndex = (eepromNextCellIndex + 1) % EEPROM_CELL_COUNT;
        if (eepromNextCellIndex == initialNext) { 
            DEBUG_PRINTLN("CRITICAL ERROR: All EEPROM cells blacklisted!");
            break; 
        }
    }
  } else {
    modeToSet = DEFAULT_MODE;
    brightnessToSet = DEFAULT_BRIGHTNESS;
    eepromNextCellIndex = 0; 
    DEBUG_PRINTLN("No valid EEPROM cell found. Using defaults.");
    for(int i=0; i<EEPROM_CELL_COUNT; ++i) eepromCellRetries[i] = 0;
  }
}

// ✅ SMART EEPROM WITH ±3% THRESHOLD & 2-SECOND DELAY
void saveStateToEEPROM(bool forceSave) {
  unsigned long currentTime = millis();

  // Don't save if system was never powered on (unless forced during sleep)
  if (!systemPoweredOn && !forceSave) return;

  // ✅ CHECK: Enough time has passed since last change (2 seconds)
  if (!forceSave && timeDiff(currentTime, brightProfile.lastChangeTime) < 2000) {
    flags.eepromWritePending = true;
    return;
  }

  // ✅ CHECK: ±3% threshold for brightness changes
  uint8_t brightnessDelta = abs((int)ledBrightness - (int)brightProfile.lastSavedBrightness);
  uint8_t threePercentThreshold = (ledBrightness * 3) / 100;
  if (threePercentThreshold < 3) threePercentThreshold = 3; // Minimum 3 points
  
  if (!forceSave && brightnessDelta < threePercentThreshold && 
      currentMode == lastSavedMode) {
    return; // No significant change
  }

  // ✅ THROTTLE: Standard 30-second throttle for writes
  if (!forceSave && timeDiff(currentTime, lastEepromWriteTime) < EEPROM_WRITE_THROTTLE_MS) {
    flags.eepromWritePending = true; 
    return;
  }

  int initialNext = eepromNextCellIndex;
  while (eepromCellRetries[eepromNextCellIndex] >= EEPROM_MAX_RETRIES) {
      DEBUG_PRINT_VAR("Skipping blacklisted cell for write: ", eepromNextCellIndex);
      eepromNextCellIndex = (eepromNextCellIndex + 1) % EEPROM_CELL_COUNT;
      if (eepromNextCellIndex == initialNext) { 
          DEBUG_PRINTLN("CRITICAL ERROR: All EEPROM cells blacklisted! Cannot save state.");
          flags.eepromWritePending = true; 
          return; 
      }
  }

  int addr = EEPROM_START_ADDR + eepromNextCellIndex * EEPROM_BLOCK_SIZE;
  byte crc = calculateCRC8(currentMode, ledBrightness);

  DEBUG_PRINT_VAR("Writing to EEPROM Cell: ", eepromNextCellIndex);
  DEBUG_PRINT_VAR(", Mode: ", currentMode);
  DEBUG_PRINT_VAR(", Brightness: ", ledBrightness);
  DEBUG_PRINTLN_VAR(", CRC: ", crc);

  EEPROM.update(addr, currentMode);
  EEPROM.update(addr + 1, ledBrightness);
  EEPROM.update(addr + 2, crc);

  byte verifyMode = EEPROM.read(addr);
  byte verifyBrightness = EEPROM.read(addr + 1);
  byte verifyCRC = EEPROM.read(addr + 2);
  bool writeOk = (verifyMode == currentMode && verifyBrightness == ledBrightness && verifyCRC == crc);

  if (writeOk) {
      DEBUG_PRINTLN("EEPROM write verified.");
      eepromCellRetries[eepromNextCellIndex] = 0; 
      lastEepromWriteTime = currentTime;
      lastSavedMode = currentMode;
      lastSavedBrightness = ledBrightness;
      flags.eepromWritePending = false;
      eepromNextCellIndex = (eepromNextCellIndex + 1) % EEPROM_CELL_COUNT; 
      initialNext = eepromNextCellIndex;
      while (eepromCellRetries[eepromNextCellIndex] >= EEPROM_MAX_RETRIES) {
          eepromNextCellIndex = (eepromNextCellIndex + 1) % EEPROM_CELL_COUNT;
          if (eepromNextCellIndex == initialNext) break; 
      }

  } else {
      DEBUG_PRINTLN("ERROR: EEPROM write verification failed!");
      eepromCellRetries[eepromNextCellIndex]++; 
      DEBUG_PRINT_VAR("Retry count for cell ", eepromNextCellIndex);
      DEBUG_PRINTLN_VAR(": ", eepromCellRetries[eepromNextCellIndex]);
      flags.eepromWritePending = true; 
  }
}

//=========================================================
// === PHẦN 12: ENERGY SAVING ===
//=========================================================

void handleEnergySave(unsigned long currentTime) {
  if (!systemPoweredOn) return;
  
  // ============================================
  // 🔧 FINAL CRITICAL FIX: Grace Period Expiration Logic
  // ============================================
  // ✅ Clear grace period after timeout
  if (gracePeriodActive && currentTime >= gracePeriodEndTime) {
    gracePeriodActive = false;
    DEBUG_PRINTLN("Grace period expired - energy saving restored");
  }
  
  // Skip energy saving logic if currently dimming or transitioning
  if (currentSystemState == STATE_DIMMING || currentSystemState == STATE_TRANSITIONING) {
      lastInteractionTime = currentTime; // Reset idle timer during these states
      return;
  }
  
  unsigned long idleTime = timeDiff(currentTime, lastInteractionTime);
  byte newLevel = 0;

  if (idleTime >= ENERGY_SAVE_5HOUR) newLevel = 7;
  else if (idleTime >= ENERGY_SAVE_3HOUR) newLevel = 6;
  else if (idleTime >= ENERGY_SAVE_2HOUR) newLevel = 5;
  else if (idleTime >= ENERGY_SAVE_1HOUR) newLevel = 4;
  else if (idleTime >= ENERGY_SAVE_8MIN) newLevel = 3;
  else if (idleTime >= ENERGY_SAVE_5MIN) newLevel = 2;
  else if (idleTime >= ENERGY_SAVE_3MIN) newLevel = 1;
  else newLevel = 0;

  if (newLevel != energySaveLevel) {
    DEBUG_PRINTLN_VAR("Energy Save Level changed to: ", newLevel);
    energySaveLevel = newLevel;
    applySavingLevel(energySaveLevel);
  }

  // Handle pending EEPROM writes during idle periods
  if (flags.eepromWritePending && timeDiff(currentTime, lastEepromWriteTime) >= EEPROM_WRITE_THROTTLE_MS) {
      saveStateToEEPROM(true); 
  }
}

// ============================================
// 🔧 CRITICAL FIX: Energy Saving Progressive Logic - Complete Rewrite
// ============================================
void applySavingLevel(byte level) {
  if (!systemPoweredOn) return;
  
  // ✅ Grace Period Override
  if (gracePeriodActive && millis() < gracePeriodEndTime) {
    DEBUG_PRINTLN("Grace period active - energy saving suspended");
    return; // Skip energy saving during grace period
  }
  
  int baseBrightness = ledBrightness;
  unsigned long fadeDuration = 2000;

  // ✅ PROGRESSIVE LOGIC: Different factors for LED1-3 vs LED4
  float led13Factor = 1.0f;
  float led4Factor = 1.0f;
  
  switch (level) {
    case 0: // Normal operation
      led13Factor = 1.0f;
      led4Factor = 1.0f;
      DEBUG_PRINTLN("Energy Level 0: Normal (100%/100%)");
      break;
      
    case 1: // 3-5min: LED4 ONLY dim to 80%, LED1-3 stay 100%
      led13Factor = 1.0f; // ✅ LED1-3 unchanged
      led4Factor = DIM_3_TO_5MIN_LED4_PERCENT; // 0.8f
      DEBUG_PRINTLN("Energy Level 1: LED4->80%, LED1-3->100%");
      break;
      
    case 2: // 5-8min: LED4 ONLY dim to 70%, LED1-3 stay 100%  
      led13Factor = 1.0f; // ✅ LED1-3 unchanged
      led4Factor = DIM_5_TO_8MIN_LED4_PERCENT; // 0.7f
      DEBUG_PRINTLN("Energy Level 2: LED4->70%, LED1-3->100%");
      break;
      
    case 3: // 8min-1h: LED1-3 start dimming to 90%, LED4 continue at 70%
      led13Factor = DIM_8MIN_LED13_PERCENT; // 0.9f
      led4Factor = DIM_8MIN_LED4_PERCENT; // 0.7f  
      DEBUG_PRINTLN("Energy Level 3: LED4->70%, LED1-3->90%");
      break;
      
    case 4: // 1-2h: LED1-3 to 85%, LED4 maintain 70%
      led13Factor = DIM_1HOUR_LED13_PERCENT; // 0.85f
      led4Factor = DIM_1HOUR_LED4_PERCENT; // 0.7f
      DEBUG_PRINTLN("Energy Level 4: LED4->70%, LED1-3->85%");
      break;
      
    case 5: // 2-3h: LED1-3 to 80%, LED4 to 60%
      led13Factor = DIM_2HOUR_LED13_PERCENT; // 0.8f
      led4Factor = DIM_2HOUR_LED4_PERCENT; // 0.6f
      DEBUG_PRINTLN("Energy Level 5: LED4->60%, LED1-3->80%");
      break;
      
    case 6: // 3-5h: LED1-3 to 75%, LED4 to 40%
      led13Factor = DIM_3HOUR_LED13_PERCENT; // 0.75f
      led4Factor = DIM_3HOUR_LED4_PERCENT; // 0.4f
      DEBUG_PRINTLN("Energy Level 6: LED4->40%, LED1-3->75%");
      break;
      
    case 7: // Sleep
      DEBUG_PRINTLN("Energy Level 7: Entering sleep");
      if (currentSystemState != STATE_SLEEP) {
        unsigned long sleepDelayStart = millis();
        while(timeDiff(millis(), sleepDelayStart) < 50) { 
          updateFades(millis()); 
          wdt_reset(); 
        }
        systemPoweredOn = false;
        enterDeepSleep();
      }
      return;
  }

  // ✅ Apply progressive dimming based on current mode
  if (currentMode == 2) {
    // ✅ BREATHING MODE: Update factor for LEDs 1-3, direct control for LED4
    breathingEnergySaveFactor = led13Factor;
    DEBUG_PRINTLN_VAR("Breathing energy factor updated: ", breathingEnergySaveFactor);
    
    // LED4 gets direct brightness control (unless dimming)
    if (currentSystemState != STATE_DIMMING) {
      int targetLED4 = max(MIN_BRIGHTNESS, (int)(baseBrightness * led4Factor));
      startFade(3, targetLED4, fadeDuration, PRIO_DIMMING);
    }
    
  } else {
    // ✅ NON-BREATHING MODES: Direct fade control for both LED groups
    
    // Apply to LEDs 1-3 (only if factor changed from 1.0f)
    if (led13Factor < 1.0f) {
      int targetLED13 = max(MIN_BRIGHTNESS, (int)(baseBrightness * led13Factor));
      for (int i = 0; i < 3; i++) {
        startFade(i, targetLED13, fadeDuration, PRIO_DIMMING);
      }
      DEBUG_PRINTLN_VAR("LEDs 1-3 dimmed to: ", targetLED13);
    }
    
    // Apply to LED4 (unless currently dimming)
    if (currentSystemState != STATE_DIMMING && led4Factor < 1.0f) {
      int baseForLED4;
      
      // ✅ CRITICAL FIX: Respect LED4 chosen brightness
      if (led4ChosenBrightness != 0) {
        baseForLED4 = led4ChosenBrightness;
        DEBUG_PRINTLN("Energy saving using LED4 chosen brightness as base");
      } else {
        baseForLED4 = baseBrightness;
      }
      
      int targetLED4 = max(MIN_BRIGHTNESS, (int)(baseForLED4 * led4Factor));
      startFade(3, targetLED4, fadeDuration, PRIO_DIMMING);
      DEBUG_PRINTLN_VAR("LED4 energy saving applied to: ", targetLED4);
    }
  }
}

//=========================================================
// === PHẦN 13: ENHANCED FADE ENGINE & LED4 CONTROLLER ===
//=========================================================

// ✅ ENHANCED EASING FUNCTIONS
float calculateEasedProgress(float t, uint8_t fadeType, uint8_t stageIndex) {
  switch(fadeType) {
    case FADE_LINEAR:
      return t;
      
    case FADE_GAMMA_SMOOTH:
      return easeInOutCubic(t); // Ultra smooth for premium feel
      
    case FADE_BREATHING:
      return easeInOutSine(t); // Natural breathing motion
      
    case FADE_SMART_CONTEXT:
      // Different easing per stage for complex transitions
      if (stageIndex == 0) return easeInOutCubic(t); // Smooth start
      if (stageIndex == 1) return t; // Linear middle  
      return easeInOutSine(t); // Gentle end
      
    default:
      return easeInOutCubic(t);
  }
}

// ✅ ENHANCED startFade with fade type support
void startFade(int ledIndex, int toVal, unsigned long durVal, uint8_t priority, uint8_t fadeType) {
  if (!systemPoweredOn && priority < PRIO_CRITICAL) { 
      return;
  }
  
  if (ledIndex < 0 || ledIndex >= 4) return;

#ifdef DEBUG
  // digitalWrite(DEBUG_PIN_FADE_START, HIGH);
  // delayMicroseconds(10);
  // digitalWrite(DEBUG_PIN_FADE_START, LOW);
#endif

  toVal = constrain(toVal, 0, MAX_BRIGHTNESS); // Allow fading to 0 for sleep

  // Optimization: If already at target and not fading, do nothing
  if (ledState[ledIndex] == toVal && !fades[ledIndex].active) {
    return;
  }

  // Priority check: Don't start lower priority fade if one is active
  if (fades[ledIndex].active && priority < fades[ledIndex].priority) {
    return;
  }

  // If starting a new fade of same or higher priority, cancel the old one implicitly
  unsigned long currentTime = millis();
  fades[ledIndex].active = true;
  fades[ledIndex].priority = priority;
  fades[ledIndex].fade_type = fadeType;
  fades[ledIndex].stage_count = 1;
  fades[ledIndex].current_stage = 0;
  fades[ledIndex].from_val = ledState[ledIndex]; // Start from current actual value
  fades[ledIndex].to_val = toVal;
  fades[ledIndex].start_time_ms = currentTime;
  fades[ledIndex].duration_ms = (durVal == 0) ? 1 : durVal; // Avoid division by zero
  
  // Set up single stage
  fades[ledIndex].stages[0] = {toVal, durVal, 1}; // Default cubic easing
}

// ✅ MULTI-STAGE FADE SUPPORT
void startMultiStageFade(int ledIndex, FadeStage* stages, uint8_t stageCount, uint8_t priority) {
  if (!enableMultiStageFades) {
    // Fallback to simple fade with last stage
    if (stageCount > 0) {
      startFade(ledIndex, stages[stageCount-1].target_val, stages[stageCount-1].duration_ms, priority, FADE_GAMMA_SMOOTH);
    }
    return;
  }
  
  if (ledIndex < 0 || ledIndex >= 4 || stageCount == 0 || stageCount > 3) return;
  
  fades[ledIndex].active = true;
  fades[ledIndex].priority = priority;
  fades[ledIndex].fade_type = FADE_SMART_CONTEXT;
  fades[ledIndex].stage_count = stageCount;
  fades[ledIndex].current_stage = 0;
  fades[ledIndex].from_val = ledState[ledIndex];
  
  // Copy stages
  for(int i = 0; i < stageCount; i++) {
    fades[ledIndex].stages[i] = stages[i];
  }
  
  // Set first stage
  fades[ledIndex].to_val = stages[0].target_val;
  fades[ledIndex].duration_ms = stages[0].duration_ms;
  fades[ledIndex].start_time_ms = millis();
}

// ✅ LED4 CONTROLLER FUNCTIONS
void setLED4Background(uint8_t brightness, bool immediate) {
  if (!systemPoweredOn && brightness > 0) return;
  
  // ✅ CRITICAL FIX: Respect chosen brightness từ DIM
  if (led4ChosenBrightness != 0) {
    led4Ctrl.targetLevel = led4ChosenBrightness;
    DEBUG_PRINTLN_VAR("LED4 using chosen brightness: ", led4ChosenBrightness);
  } else {
    led4Ctrl.targetLevel = constrain(brightness, MIN_BRIGHTNESS, MAX_BRIGHTNESS);
    DEBUG_PRINTLN_VAR("LED4 using calculated brightness: ", brightness);
  }
  
  led4Ctrl.needsUpdate = true;
  
  if (immediate) {
    led4Ctrl.backgroundLevel = led4Ctrl.targetLevel;
    analogWrite(ledPins[3], gammaCorrect(led4Ctrl.backgroundLevel));
    ledState[3] = led4Ctrl.backgroundLevel;
    led4Ctrl.needsUpdate = false;
  } else {
    startFade(3, led4Ctrl.targetLevel, 800, PRIO_DIMMING, FADE_GAMMA_SMOOTH);
  }
}

void updateLED4Controller(unsigned long currentTime) {
  // Update background level tracking
  if (led4Ctrl.needsUpdate && timeDiff(currentTime, led4Ctrl.lastUpdate) > 50) {
    led4Ctrl.lastUpdate = currentTime;
    led4Ctrl.backgroundLevel = ledState[3]; // Track actual state
    
    if (abs(led4Ctrl.backgroundLevel - led4Ctrl.targetLevel) <= 2) {
      led4Ctrl.needsUpdate = false; // Close enough
    }
  }
}

uint8_t calculateLED4Level(uint8_t userBrightness) {
  // Smart calculation: min(90%, user_brightness)
  uint8_t ninetyPercent = (userBrightness * 90) / 100;
  return max(MIN_BRIGHTNESS, min(ninetyPercent, userBrightness));
}

// ✅ BRIGHTNESS MANAGEMENT FUNCTIONS
void onBrightnessChanged(uint8_t newBrightness) {
  ledBrightness = newBrightness;
  brightProfile.userSetBrightness = newBrightness;
  brightProfile.lastChangeTime = millis();
  
  // Update LED4 background immediately
  uint8_t led4Level = calculateLED4Level(newBrightness);
  setLED4Background(led4Level, false);
  
  // Calculate Mode 1 final target
  if (newBrightness > (MAX_BRIGHTNESS * 90) / 100) {
    brightProfile.mode1FinalTarget = (MAX_BRIGHTNESS * 90) / 100; // Force 90%
  } else {
    brightProfile.mode1FinalTarget = newBrightness; // Keep user setting
  }
}

// ✅ FEATURE INITIALIZATION & MEMORY MANAGEMENT
void initializeFeatures() {
  int freeRAM = getFreeMemory();
  
  if (freeRAM < 400) {
    enableMultiStageFades = false;
    DEBUG_PRINTLN("Multi-stage fades disabled (low memory)");
  }
  
  if (freeRAM < 350) {
    enableUltraSmoothing = false;
    DEBUG_PRINTLN("Ultra-smoothing disabled (low memory)");
  }
  
  if (freeRAM < 300) {
    enableAdvancedTransitions = false;
    DEBUG_PRINTLN("Advanced transitions disabled (low memory)");
  }
}

void checkMemoryForFeatures() {
  int freeRAM = getFreeMemory();
  if (freeRAM < 300) {
    DEBUG_PRINTLN("WARNING: Low memory for advanced features!");
    // Fallback to basic modes if needed
  }
}

//=========================================================
// === PHẦN: DYNAMIC STEP CALCULATION & LED4 FUNCTIONS ===
//=========================================================

// ✅ NEW: Calculate dynamic step based on current brightness
int calculateDimStep(int currentBrightness) {
    // 2.5% của current brightness, min=1, max=8 cho stability
    int step = max(1, min(8, (int)(currentBrightness * 0.025f)));
    DEBUG_PRINT_VAR("Dynamic step for brightness ", currentBrightness);
    DEBUG_PRINTLN_VAR(": ", step);
    return step;
}

// ✅ NEW: Reset LED4 chosen brightness khi cần
void resetLED4ChosenBrightness() {
    led4ChosenBrightness = 0;
    DEBUG_PRINTLN("LED4 chosen brightness reset - will use calculated values");
}

// ============================================
// 🎯 VERIFICATION: All DIM Functions Working Correctly
// ============================================

// ✅ CONFIRMED WORKING FUNCTIONS:
// - handleDimming() ✅ Correct touch release handling
// - applyDimmingToAllLEDs() ✅ Smart mode-specific dimming
// - updateTargetStatesAfterDim() ✅ OFF LEDs brightness storage
// - calculateDimStep() ✅ Dynamic step calculation
// - startDimming() ✅ Smart direction logic
// - stopDimming() ✅ LED4 chosen brightness retention


//=========================================================
// === PHẦN 14: UTILITIES & DEBUG ===
//=========================================================

int gammaCorrect(int brightness) {
  if (brightness <= 0) return 0;
  if (brightness >= 255) return 255;
  // Read from PROGMEM table
  return pgm_read_byte(&gamma8[brightness]);
}

// Calculate difference handling timer overflow
inline unsigned long timeDiff(unsigned long current, unsigned long previous) {
  return current - previous; 
}

// Easing function: Sine In/Out
float easeInOutSine(float t) {
  // t ranges from 0.0 to 1.0
  return -(cos(PI * t) - 1.0f) / 2.0f;
}

// Easing function: Cubic In/Out
float easeInOutCubic(float t) {
  // t ranges from 0.0 to 1.0
  if (t < 0.5f) {
    return 4.0f * t * t * t;
  } else {
    float f = -2.0f * t + 2.0f;
    return 1.0f - f * f * f / 2.0f;
  }
}

void resetBreathingTiming(unsigned long currentTime) {
    mode2_breathingStartTime = currentTime;
    mode2_lastBreathingUpdate = currentTime;
    mode2_needsTimingReset = false;
    DEBUG_PRINTLN("Breathing timing explicitly reset.");
}

void resetToDefault() {
  if (!systemPoweredOn) return;
  
  currentMode = DEFAULT_MODE;
  ledBrightness = DEFAULT_BRIGHTNESS;
  energySaveLevel = 0;
  tapCount = 0;

  // ✅ Reset LED4 chosen brightness
  resetLED4ChosenBrightness();

  // Reset EEPROM retries
  for(int i=0; i<EEPROM_CELL_COUNT; ++i) eepromCellRetries[i] = 0;
  eepromNextCellIndex = 0;
  saveStateToEEPROM(true); // Force save default state

  setupMode(currentMode); // Setup the default mode
  applyBrightnessToLeds(); // Apply initial brightness
  lastInteractionTime = millis();

  DEBUG_PRINTLN("System Reset to Defaults.");
}

// Sets initial LED brightness based on mode (used in setup/wake/power-on)
void applyBrightnessToLeds() {
    if (!systemPoweredOn) {
         DEBUG_PRINTLN("applyBrightnessToLeds skipped (systemPoweredOn=false)");
         // Ensure LEDs are off if called when system should be off
         for(int i=0; i<4; ++i) {
             if (ledState[i] != 0) ledState[i] = 0;
             analogWrite(ledPins[i], 0);
         }
         return;
    }
    
    // --- Original logic continues --- 
    cancelAllFades(); // Stop any previous fades
    int led4Target = max(MIN_BRIGHTNESS, (int)(ledBrightness * LED4_IDLE_PERCENT));
    switch (currentMode) {
        case 1: // Slow Fade starts syncing, handled in setupMode/handleMode
            // Set LED4 initial state
            ledState[3] = led4Target;
            // LEDs 1-3 state will be set by the mode logic
            break; 
        case 2: // Breathing - Start LEDs 1-3 at min, LED4 at idle
        case 4: // Pattern - Start LEDs 1-3 at MIN, LED4 at idle
            for(int i=0; i<3; ++i) ledState[i] = MIN_BRIGHTNESS;
            ledState[3] = led4Target;
            break;
        case 3: // Rain Drops - Start all at MIN, LED4 at idle
             for(int i=0; i<3; ++i) ledState[i] = MIN_BRIGHTNESS;
             ledState[3] = led4Target;
             break;
        default:
             for(int i=0; i<4; ++i) ledState[i] = 0;
             break;
    }
    // Apply initial state immediately
    for(int i=0; i<4; ++i) {
        analogWrite(ledPins[i], gammaCorrect(ledState[i]));
        targetLedState[i] = ledState[i]; // Update target state as well
    }
}

#ifdef DEBUG
int getFreeMemory() {
  extern int __heap_start, *__brkval;
  int v;
  return (int) &v - (__brkval == 0 ? (int) &__heap_start : (int) __brkval);
}

void reportMemoryUsage() {
  DEBUG_PRINTLN_VAR("Free RAM: ", getFreeMemory());
}

void printSystemState(SystemState stateToPrint) { 
    switch(stateToPrint) {
        case STATE_SLEEP: Serial.print(F("SLEEP")); break;
        case STATE_WAKE_UP: Serial.print(F("WAKE_UP")); break;
        case STATE_IDLE: Serial.print(F("IDLE")); break;
        case STATE_MODE_ACTIVE: Serial.print(F("MODE_ACTIVE")); break;
        case STATE_DIMMING: Serial.print(F("DIMMING")); break;
        case STATE_TRANSITIONING: Serial.print(F("TRANSITIONING")); break;
        default: Serial.print(F("UNKNOWN")); break;
    }
}

#ifdef DEBUG_MODE1
void printMode1State(SlowFadePhase phase) {
    switch(phase) {
        case M1_IDLE: Serial.print(F("M1_IDLE")); break;
        case M1_ANALYZE_CURRENT: Serial.print(F("M1_ANALYZE_CURRENT")); break;
        case M1_SYNC_TO_MAX: Serial.print(F("M1_SYNC_TO_MAX")); break;
        case M1_FADE_TO_TARGET: Serial.print(F("M1_FADE_TO_TARGET")); break;
        case M1_HOLD_AT_TARGET: Serial.print(F("M1_HOLD_AT_TARGET")); break;
        case M1_FADE_TO_FINAL: Serial.print(F("M1_FADE_TO_FINAL")); break;
        case M1_SETTLED: Serial.print(F("M1_SETTLED")); break;
        default: Serial.print(F("M1_UNKNOWN")); break;
    }
}
#endif // DEBUG_MODE1

void systemHealthCheck() {
  static unsigned long lastHealthCheck = 0;
  if (timeDiff(millis(), lastHealthCheck) < 5000) return;
  lastHealthCheck = millis();
  
  // Check for stuck fades (only if system is ON)
  if (systemPoweredOn) {
      for (int i = 0; i < 4; i++) {
        if (fades[i].active && 
            timeDiff(millis(), fades[i].start_time_ms) > fades[i].duration_ms + 5000) { // Increased tolerance
          DEBUG_PRINT_VAR("ERROR: Stuck fade on LED ", i);
          DEBUG_PRINTLN_VAR(" Duration: ", fades[i].duration_ms);
          fades[i].active = false; // Force cancel
        }
      }
  }
  
  // Check memory
  if (getFreeMemory() < 100) { 
    DEBUG_PRINTLN("WARNING: Low memory!");
  }
}

void monitorMemory() {
    static int minFreeMemory = 9999;
    int free = getFreeMemory();
    
    if (free < minFreeMemory) {
        minFreeMemory = free;
        DEBUG_PRINT_VAR("New Min Free RAM: ", minFreeMemory);
        
        if (minFreeMemory < 200) {
            DEBUG_PRINTLN("WARNING: CRITICAL MEMORY!");
        }
    }
}

void debugFadeSystem() {
  static unsigned long lastFadeDebug = 0;
  unsigned long now = millis();
  
  if (timeDiff(now, lastFadeDebug) > 1000) { 
    lastFadeDebug = now;
    
    DEBUG_PRINT("Fades: ");
    for (int i = 0; i < 4; i++) {
      Serial.print(i); Serial.print(F(":"));
      if (fades[i].active) {
        Serial.print(fades[i].from_val); Serial.print(F("->")); Serial.print(fades[i].to_val);
        Serial.print(F("(P")); Serial.print(fades[i].priority); Serial.print(F(") "));
      } else {
        Serial.print(F("off "));
      }
    }
    Serial.println();
  }
}

void debugTouchSensor() {
    DEBUG_PRINT("Touch Raw: ");
    Serial.print(digitalRead(touchPin));
    DEBUG_PRINT_VAR(" Debounced: ", flags.touchState);
    DEBUG_PRINTLN_VAR(" Tap Count: ", tapCount);
}

void printFullSystemState() {
    Serial.println(F("\n=== SYSTEM STATE ==="));
    DEBUG_PRINT("State: ");
    printSystemState(currentSystemState);
    DEBUG_PRINTLN_VAR(" Powered ON: ", systemPoweredOn);
    
    DEBUG_PRINT_VAR("Mode: ", currentMode);
    DEBUG_PRINTLN_VAR(" Brightness: ", ledBrightness);
    
    DEBUG_PRINT("LED States (Actual): ");
    for(int i = 0; i < 4; i++) {
        Serial.print(ledState[i]);
        Serial.print(F(" "));
    }
    Serial.println();
    DEBUG_PRINT("LED States (Target): ");
    for(int i = 0; i < 4; i++) {
        Serial.print(targetLedState[i]);
        Serial.print(F(" "));
    }
    Serial.println();
    
    DEBUG_PRINT("Active Fades: ");
    for(int i = 0; i < 4; i++) {
        if (fades[i].active) {
            Serial.print(i); Serial.print(F("(P")); Serial.print(fades[i].priority); Serial.print(F(") "));
        }
    }
    Serial.println();
    
    DEBUG_PRINTLN_VAR("Energy Level: ", energySaveLevel);
    DEBUG_PRINT("EEPROM Next Cell: "); Serial.print(eepromNextCellIndex);
    DEBUG_PRINT(" Retries: [");
    for(int i=0; i<EEPROM_CELL_COUNT; ++i) { Serial.print(eepromCellRetries[i]); Serial.print(" "); }
    Serial.println("]");
    
    Serial.println(F("==================\n"));
}

void verifyEEPROMIntegrity() {
    DEBUG_PRINTLN("=== EEPROM CHECK ===");
    
    for (int i = 0; i < EEPROM_CELL_COUNT; i++) {
        int addr = EEPROM_START_ADDR + i * EEPROM_BLOCK_SIZE;
        byte mode = EEPROM.read(addr);
        byte brightness = EEPROM.read(addr + 1);
        byte crc = EEPROM.read(addr + 2);
        byte calcCrc = calculateCRC8(mode, brightness);
        
        DEBUG_PRINT_VAR("Cell ", i);
        DEBUG_PRINT_VAR(": M=", mode);
        DEBUG_PRINT_VAR(" B=", brightness);
        DEBUG_PRINT_VAR(" CRC=", crc);
        DEBUG_PRINT_VAR(" (calc=", calcCrc);
        DEBUG_PRINT_VAR(") Retries=", eepromCellRetries[i]);
        DEBUG_PRINT(" ");
        Serial.println(crc == calcCrc ? F("OK") : F("FAIL"));
    }
}

#endif // DEBUG

//=========================================================
// === PHẦN 14: TEST FUNCTIONS (Optional) ===
//=========================================================

#ifdef RUN_TESTS
void testModeTransitions() {
  DEBUG_PRINTLN("--- Testing Mode Transitions --- ");
  // Ensure system is ON for testing transitions
  if (!systemPoweredOn) {
      DEBUG_PRINTLN("Powering ON system for test...");
      systemPoweredOn = true;
      setupMode(currentMode);
      applyBrightnessToLeds();
      delay(1000); // Wait for initial fades
  }
  
  byte startMode = currentMode;
  for (int i = 1; i <= 5; i++) { // Cycle through modes + 1
    byte from = currentMode;
    byte to = (from % 4) + 1;
    DEBUG_PRINT_VAR("Testing Transition: ", from);
    DEBUG_PRINTLN_VAR(" -> ", to);
    
    startTransition(from, to);
    unsigned long start = millis();
    while(currentSystemState == STATE_TRANSITIONING && timeDiff(millis(), start) < (TRANSITION_TIMEOUT + 1000)) {
        loop(); // Let loop handle updates
    }
    if (currentSystemState != STATE_MODE_ACTIVE) {
        DEBUG_PRINTLN("ERROR: Failed to transition to MODE_ACTIVE");
    } else {
        DEBUG_PRINTLN_VAR("Transition OK. Current Mode: ", currentMode);
    }
    delay(2000); // Observe the new mode briefly
  }
  // Restore original mode
  startTransition(currentMode, startMode);
   unsigned long start = millis();
    while(currentSystemState == STATE_TRANSITIONING && timeDiff(millis(), start) < (TRANSITION_TIMEOUT + 1000)) {
        loop(); 
    }
  DEBUG_PRINTLN("--- Mode Transition Test Complete --- ");
}

void testEEPROM() {
  DEBUG_PRINTLN("--- Testing EEPROM --- ");
  byte originalMode = currentMode;
  byte originalBrightness = ledBrightness;
  bool fail = false;

  DEBUG_PRINTLN("Performing write/read cycles...");
  for (int i = 0; i < EEPROM_CELL_COUNT * 2; i++) { 
    byte testMode = (i % 4) + 1;
    byte testBrightness = 100 + (i * 15) % 150;
    if (testBrightness < MIN_BRIGHTNESS) testBrightness = MIN_BRIGHTNESS;
    
    currentMode = testMode;
    ledBrightness = testBrightness;
    saveStateToEEPROM(true); // Force save
    delay(10); 
    
    byte readMode = 0;
    byte readBrightness = 0;
    readStateFromEEPROM(readMode, readBrightness);
    
    if (readMode != currentMode || readBrightness != ledBrightness) {
        DEBUG_PRINT_VAR("EEPROM Read Mismatch Cycle ", i);
        DEBUG_PRINT_VAR(" Expected M=", currentMode); DEBUG_PRINT_VAR(" B=", ledBrightness);
        DEBUG_PRINT_VAR(" Got M=", readMode); DEBUG_PRINTLN_VAR(" B=", readBrightness);
    }
  }
  
  currentMode = originalMode;
  ledBrightness = originalBrightness;
  saveStateToEEPROM(true);
  delay(10);
  byte finalReadMode = 0;
  byte finalReadBrightness = 0;
  readStateFromEEPROM(finalReadMode, finalReadBrightness);
  if (finalReadMode != currentMode || finalReadBrightness != ledBrightness) {
      DEBUG_PRINTLN("ERROR: Failed to restore original state in EEPROM!");
      fail = true;
  }

  if (!fail) {
      DEBUG_PRINTLN("EEPROM Test Complete: OK (Check logs for potential mismatches due to blacklist)");
  } else {
      DEBUG_PRINTLN("EEPROM Test Finished with ERRORS.");
  }
  verifyEEPROMIntegrity(); 
  DEBUG_PRINTLN("--- EEPROM Test Finished --- ");
}

void runComprehensiveTest() {
    DEBUG_PRINTLN("\n=== COMPREHENSIVE TEST START ===");
    
    // Test 1: Memory
    reportMemoryUsage();
    monitorMemory(); 
    
    // Test 2: EEPROM
    verifyEEPROMIntegrity();
    testEEPROM();
    
    // Test 3: Each mode transition
    testModeTransitions();
    
    // Test 4: Touch responses (Manual)
    DEBUG_PRINTLN("\n=== Touch Test - Please perform single, double, hold, 5-taps ===");
    DEBUG_PRINTLN("Ensure system powers ON with first tap.");
    delay(15000); 

    // Test 5: Final state check
    printFullSystemState();
    monitorMemory(); 
    
    DEBUG_PRINTLN("\n=== TEST COMPLETE ===");
}
#endif // RUN_TESTS

#pragma GCC pop_options
