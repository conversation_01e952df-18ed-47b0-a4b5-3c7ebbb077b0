// ============================================
// 🎨 SMOOTH FADE UPGRADE - SLEEP & POWER ON
// ============================================

// BƯỚC 1: CẬP NHẬT TIMING CONSTANTS
// Tìm section "HẰNG SỐ THỜI GIAN" (khoảng dòng 100-150) và thay đổi:

// ❌ THAY ĐỔI constants này:
// const unsigned long POWER_ON_FADE_DURATION = 1000; // Duration for fade-in on first touch

// ✅ THÀNH constants mới này:
const unsigned long POWER_ON_FADE_DURATION = 1800;   // 1.8s ultra smooth power on
const unsigned long SLEEP_FADE_DURATION = 2000;      // 2s ultra smooth sleep fade
const unsigned long PREMIUM_SLEEP_FADE = 2200;       // 2.2s for premium multi-stage sleep

// ============================================
// BƯỚC 2: NÂNG CẤP enterDeepSleep() FUNCTION
// ============================================

// Tìm function enterDeepSleep() và THAY ĐỔI section fade này:

// ❌ XÓA section cũ này:
/*
    // Only fade LEDs that are currently ON for smooth effect
    DEBUG_PRINTLN("Starting smooth sleep fade for active LEDs...");
    const unsigned long SMOOTH_SLEEP_FADE = 1000; // 1 second as user requested

    for (int i = 0; i < 4; i++) {
      if (ledState[i] > 0) {
        DEBUG_PRINT_VAR("Fading LED", i);
        DEBUG_PRINTLN_VAR(" from brightness: ", ledState[i]);
        startFade(i, 0, SMOOTH_SLEEP_FADE, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
      } else {
        DEBUG_PRINT_VAR("LED", i);
        DEBUG_PRINTLN(" already OFF - no fade needed");
      }
    }
*/

// ✅ THAY BẰNG implementation mới này:
    // ============================================
    // 🎨 ULTRA SMOOTH SLEEP FADE - PREMIUM EXPERIENCE
    // ============================================
    DEBUG_PRINTLN("Starting ULTRA SMOOTH sleep fade...");
    
    // Count active LEDs for smart timing
    int activeLEDs = 0;
    for (int i = 0; i < 4; i++) {
        if (ledState[i] > MIN_BRIGHTNESS) activeLEDs++;
    }
    
    if (activeLEDs == 0) {
        DEBUG_PRINTLN("All LEDs already OFF - quick sleep");
        // Skip fade if nothing to fade
    } else {
        DEBUG_PRINTLN_VAR("Fading ", activeLEDs);
        DEBUG_PRINTLN(" active LEDs with premium smoothness");
        
        // ✅ PREMIUM MULTI-STAGE FADE for ultra smoothness
        if (enableMultiStageFades && activeLEDs >= 2) {
            DEBUG_PRINTLN("Using PREMIUM multi-stage sleep fade");
            
            for (int i = 0; i < 4; i++) {
                if (ledState[i] > MIN_BRIGHTNESS) {
                    // Multi-stage fade: current → 30% → 0% for ultra smooth
                    FadeStage sleepStages[2] = {
                        {(int)(ledState[i] * 0.3f), 800, 1},    // 0.8s to 30% with cubic easing
                        {0, 1400, 2}                            // 1.4s to 0% with sine easing  
                    };
                    startMultiStageFade(i, sleepStages, 2, PRIO_CRITICAL);
                    DEBUG_PRINTLN_VAR("Premium sleep fade started for LED", i);
                }
            }
        } else {
            // ✅ ENHANCED single-stage fade với longer duration
            DEBUG_PRINTLN("Using enhanced single-stage sleep fade");
            
            for (int i = 0; i < 4; i++) {
                if (ledState[i] > MIN_BRIGHTNESS) {
                    // Enhanced fade: 2 seconds with ultra smooth gamma curve
                    startFade(i, 0, SLEEP_FADE_DURATION, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
                    DEBUG_PRINTLN_VAR("Enhanced sleep fade started for LED", i);
                }
            }
        }
        
        // ✅ WAIT for premium fade completion with watchdog protection
        unsigned long fadeStart = millis();
        unsigned long maxWaitTime = PREMIUM_SLEEP_FADE + 500; // Extra safety margin
        
        DEBUG_PRINTLN("Waiting for ultra smooth fade completion...");
        
        while (areFadesActive() && timeDiff(millis(), fadeStart) < maxWaitTime) {
            unsigned long now = millis();
            
            // Update fades manually for smooth experience
            updateFades(now);
            
            // Watchdog protection
            wdt_reset();
            
            // Premium timing: 10ms updates for ultra smoothness
            delay(10);
        }
        
        DEBUG_PRINTLN("Premium sleep fade completed");
    }

// ============================================
// BƯỚC 3: NÂNG CẤP POWER ON FADE (processTapAction case 1)
// ============================================

// Tìm function processTapAction() case 1 và THAY ĐỔI section power on này:

// ❌ XÓA section cũ này:
/*
        if (!systemPoweredOn) { // Only power on if system is currently OFF
            systemPoweredOn = true;
            DEBUG_PRINTLN("System powered ON by first tap action (initial)");
            // Restore last state with a fade-in effect
            // State should have been read from EEPROM in setup()
            setupMode(currentMode); 
            applyBrightnessToLeds(); // Calculate target states
            // Start fade from 0 to target state
            int initialStates[4];
            for(int i=0; i<4; ++i) initialStates[i] = targetLedState[i]; // Get target state
            for(int i=0; i<4; ++i) ledState[i] = 0; // Set current state to 0
            for(int i=0; i<4; ++i) startFade(i, initialStates[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL);
            
            lastInteractionTime = millis(); // Reset idle timer on power on
        }
*/

// ✅ THAY BẰNG implementation mới này:
        if (!systemPoweredOn) { // Only power on if system is currently OFF
            systemPoweredOn = true;
            DEBUG_PRINTLN("System powered ON - ULTRA SMOOTH fade in");
            
            // Setup mode and calculate targets
            setupMode(currentMode); 
            applyBrightnessToLeds(); // Calculate target states
            
            // ============================================
            // 🎨 ULTRA SMOOTH POWER ON FADE - PREMIUM EXPERIENCE  
            // ============================================
            
            // Count target LEDs for smart fade
            int targetLEDs = 0;
            for(int i = 0; i < 4; i++) {
                if (targetLedState[i] > MIN_BRIGHTNESS) targetLEDs++;
            }
            
            DEBUG_PRINTLN_VAR("Power on fade for ", targetLEDs);
            DEBUG_PRINTLN(" target LEDs");
            
            // Reset all LEDs to 0 for clean start
            for(int i = 0; i < 4; i++) {
                ledState[i] = 0;
                analogWrite(ledPins[i], 0);
            }
            
            // ✅ PREMIUM MULTI-STAGE POWER ON FADE
            if (enableMultiStageFades && targetLEDs >= 2) {
                DEBUG_PRINTLN("Using PREMIUM multi-stage power on fade");
                
                for(int i = 0; i < 4; i++) {
                    if (targetLedState[i] > MIN_BRIGHTNESS) {
                        // Multi-stage fade: 0 → 20% → target for ultra smooth
                        int midPoint = (int)(targetLedState[i] * 0.2f);
                        FadeStage powerOnStages[2] = {
                            {max(MIN_BRIGHTNESS, midPoint), 600, 2},    // 0.6s to 20% with sine easing
                            {targetLedState[i], 1200, 1}                // 1.2s to target with cubic easing
                        };
                        startMultiStageFade(i, powerOnStages, 2, PRIO_CRITICAL);
                        DEBUG_PRINTLN_VAR("Premium power on fade started for LED", i);
                    }
                }
            } else {
                // ✅ ENHANCED single-stage fade với longer duration
                DEBUG_PRINTLN("Using enhanced single-stage power on fade");
                
                for(int i = 0; i < 4; i++) {
                    if (targetLedState[i] > MIN_BRIGHTNESS) {
                        // Enhanced fade: 1.8 seconds with ultra smooth gamma curve
                        startFade(i, targetLedState[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
                        DEBUG_PRINTLN_VAR("Enhanced power on fade started for LED", i);
                    }
                }
            }
            
            lastInteractionTime = millis(); // Reset idle timer on power on
        }

// ============================================
// BƯỚC 4: NÂNG CẤP handleWakeUp() (Tương tự power on)
// ============================================

// Tìm function handleWakeUp() và THAY ĐỔI section fade cuối:

// ❌ XÓA section cũ này:
/*
  // Start fade from 0 to target state for power-on effect
  int initialStates[4];
  for(int i=0; i<4; ++i) initialStates[i] = targetLedState[i]; // Get target state
  for(int i=0; i<4; ++i) ledState[i] = 0; // Set current state to 0
  for(int i=0; i<4; ++i) startFade(i, initialStates[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL);
*/

// ✅ THAY BẰNG implementation mới này:
  // ============================================
  // 🎨 ULTRA SMOOTH WAKE UP FADE - PREMIUM EXPERIENCE
  // ============================================
  DEBUG_PRINTLN("Starting ULTRA SMOOTH wake up fade");
  
  // Reset all LEDs to 0 for clean wake up
  for(int i = 0; i < 4; i++) {
      ledState[i] = 0;
      analogWrite(ledPins[i], 0);
  }
  
  // Count target LEDs
  int wakeTargetLEDs = 0;
  for(int i = 0; i < 4; i++) {
      if (targetLedState[i] > MIN_BRIGHTNESS) wakeTargetLEDs++;
  }
  
  // ✅ PREMIUM wake up fade
  if (enableMultiStageFades && wakeTargetLEDs >= 2) {
      DEBUG_PRINTLN("Premium multi-stage wake up fade");
      
      for(int i = 0; i < 4; i++) {
          if (targetLedState[i] > MIN_BRIGHTNESS) {
              // Gentle wake up: 0 → 15% → target
              int wakePoint = (int)(targetLedState[i] * 0.15f);
              FadeStage wakeStages[2] = {
                  {max(MIN_BRIGHTNESS, wakePoint), 700, 2},   // 0.7s gentle start
                  {targetLedState[i], 1100, 1}               // 1.1s to full
              };
              startMultiStageFade(i, wakeStages, 2, PRIO_CRITICAL);
          }
      }
  } else {
      // Enhanced single-stage wake up
      DEBUG_PRINTLN("Enhanced single-stage wake up fade");
      
      for(int i = 0; i < 4; i++) {
          if (targetLedState[i] > MIN_BRIGHTNESS) {
              startFade(i, targetLedState[i], POWER_ON_FADE_DURATION, PRIO_CRITICAL, FADE_GAMMA_SMOOTH);
          }
      }
  }

// ============================================
// 🎯 SUMMARY OF CHANGES
// ============================================

/*
📊 UPGRADE SUMMARY:

✅ SLEEP FADE IMPROVEMENTS:
- Duration: 1s → 2-2.2s  
- Multi-stage option: current → 30% → 0%
- Enhanced easing: FADE_GAMMA_SMOOTH + multi-stage
- Premium experience với ultra smooth timing

✅ POWER ON FADE IMPROVEMENTS:  
- Duration: 1s → 1.8s
- Multi-stage option: 0 → 20% → target
- Enhanced easing: FADE_GAMMA_SMOOTH + multi-stage
- Consistent experience across power on & wake up

✅ NO IMPACT ON OTHER FEATURES:
- Dimming system unchanged ✅
- Mode logic unchanged ✅  
- Energy saving unchanged ✅
- Touch logic unchanged ✅
- All existing functionality preserved ✅

🎯 RESULT: ULTRA SMOOTH, PREMIUM FADE EXPERIENCE!
*/